openapi: 3.0.1
info:
  title: langfuse
  version: ''
  description: >-
    ## Authentication


    Authenticate with the API using [Basic
    Auth](https://en.wikipedia.org/wiki/Basic_access_authentication), get API
    keys in the project settings:


    - username: Langfuse Public Key

    - password: Langfuse Secret Key


    ## Exports


    - OpenAPI spec: https://cloud.langfuse.com/generated/api/openapi.yml

    - Postman collection:
    https://cloud.langfuse.com/generated/postman/collection.json
paths:
  /api/public/annotation-queues:
    get:
      description: Get all annotation queues
      operationId: annotationQueues_listQueues
      tags:
        - AnnotationQueues
      parameters:
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAnnotationQueues'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/annotation-queues/{queueId}:
    get:
      description: Get an annotation queue by ID
      operationId: annotationQueues_getQueue
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnotationQueue'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/annotation-queues/{queueId}/items:
    get:
      description: Get items for a specific annotation queue
      operationId: annotationQueues_listQueueItems
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          required: false
          schema:
            $ref: '#/components/schemas/AnnotationQueueStatus'
            nullable: true
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAnnotationQueueItems'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: Add an item to an annotation queue
      operationId: annotationQueues_createQueueItem
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnotationQueueItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAnnotationQueueItemRequest'
  /api/public/annotation-queues/{queueId}/items/{itemId}:
    get:
      description: Get a specific item from an annotation queue
      operationId: annotationQueues_getQueueItem
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
        - name: itemId
          in: path
          description: The unique identifier of the annotation queue item
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnotationQueueItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    patch:
      description: Update an annotation queue item
      operationId: annotationQueues_updateQueueItem
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
        - name: itemId
          in: path
          description: The unique identifier of the annotation queue item
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnotationQueueItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAnnotationQueueItemRequest'
    delete:
      description: Remove an item from an annotation queue
      operationId: annotationQueues_deleteQueueItem
      tags:
        - AnnotationQueues
      parameters:
        - name: queueId
          in: path
          description: The unique identifier of the annotation queue
          required: true
          schema:
            type: string
        - name: itemId
          in: path
          description: The unique identifier of the annotation queue item
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteAnnotationQueueItemResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/comments:
    post:
      description: >-
        Create a comment. Comments may be attached to different object types
        (trace, observation, session, prompt).
      operationId: comments_create
      tags:
        - Comments
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCommentResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentRequest'
    get:
      description: Get all comments
      operationId: comments_get
      tags:
        - Comments
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1.
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit
          required: false
          schema:
            type: integer
            nullable: true
        - name: objectType
          in: query
          description: >-
            Filter comments by object type (trace, observation, session,
            prompt).
          required: false
          schema:
            type: string
            nullable: true
        - name: objectId
          in: query
          description: >-
            Filter comments by object id. If objectType is not provided, an
            error will be thrown.
          required: false
          schema:
            type: string
            nullable: true
        - name: authorUserId
          in: query
          description: Filter comments by author user id.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCommentsResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/comments/{commentId}:
    get:
      description: Get a comment by id
      operationId: comments_get-by-id
      tags:
        - Comments
      parameters:
        - name: commentId
          in: path
          description: The unique langfuse identifier of a comment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/dataset-items:
    post:
      description: Create a dataset item
      operationId: datasetItems_create
      tags:
        - DatasetItems
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasetItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDatasetItemRequest'
    get:
      description: Get dataset items
      operationId: datasetItems_list
      tags:
        - DatasetItems
      parameters:
        - name: datasetName
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sourceTraceId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sourceObservationId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDatasetItems'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/dataset-items/{id}:
    get:
      description: Get a dataset item
      operationId: datasetItems_get
      tags:
        - DatasetItems
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasetItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: >-
        Delete a dataset item and all its run items. This action is
        irreversible.
      operationId: datasetItems_delete
      tags:
        - DatasetItems
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteDatasetItemResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/dataset-run-items:
    post:
      description: Create a dataset run item
      operationId: datasetRunItems_create
      tags:
        - DatasetRunItems
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasetRunItem'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDatasetRunItemRequest'
    get:
      description: List dataset run items
      operationId: datasetRunItems_list
      tags:
        - DatasetRunItems
      parameters:
        - name: datasetId
          in: query
          required: true
          schema:
            type: string
        - name: runName
          in: query
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
        - name: response
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/PaginatedDatasetRunItems'
      responses:
        '204':
          description: ''
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/v2/datasets:
    get:
      description: Get all datasets
      operationId: datasets_list
      tags:
        - Datasets
      parameters:
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDatasets'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: Create a dataset
      operationId: datasets_create
      tags:
        - Datasets
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dataset'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDatasetRequest'
  /api/public/v2/datasets/{datasetName}:
    get:
      description: Get a dataset
      operationId: datasets_get
      tags:
        - Datasets
      parameters:
        - name: datasetName
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dataset'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/datasets/{datasetName}/runs/{runName}:
    get:
      description: Get a dataset run and its items
      operationId: datasets_getRun
      tags:
        - Datasets
      parameters:
        - name: datasetName
          in: path
          required: true
          schema:
            type: string
        - name: runName
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasetRunWithItems'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: Delete a dataset run and all its run items. This action is irreversible.
      operationId: datasets_deleteRun
      tags:
        - Datasets
      parameters:
        - name: datasetName
          in: path
          required: true
          schema:
            type: string
        - name: runName
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteDatasetRunResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/datasets/{datasetName}/runs:
    get:
      description: Get dataset runs
      operationId: datasets_getRuns
      tags:
        - Datasets
      parameters:
        - name: datasetName
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDatasetRuns'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/health:
    get:
      description: Check health of API and database
      operationId: health_health
      tags:
        - Health
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
        '503':
          description: ''
  /api/public/ingestion:
    post:
      description: >-
        Batched ingestion for Langfuse Tracing.

        If you want to use tracing via the API, such as to build your own
        Langfuse client implementation, this is the only API route you need to
        implement.


        Within each batch, there can be multiple events.

        Each event has a type, an id, a timestamp, metadata and a body.

        Internally, we refer to this as the "event envelope" as it tells us
        something about the event but not the trace.

        We use the event id within this envelope to deduplicate messages to
        avoid processing the same event twice, i.e. the event id should be
        unique per request.

        The event.body.id is the ID of the actual trace and will be used for
        updates and will be visible within the Langfuse App.

        I.e. if you want to update a trace, you'd use the same body id, but
        separate event IDs.


        Notes:

        - Introduction to data model:
        https://langfuse.com/docs/tracing-data-model

        - Batch sizes are limited to 3.5 MB in total. You need to adjust the
        number of events per batch accordingly.

        - The API does not return a 4xx status code for input errors. Instead,
        it responds with a 207 status code, which includes a list of the
        encountered errors.
      operationId: ingestion_batch
      tags:
        - Ingestion
      parameters: []
      responses:
        '207':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IngestionResponse'
              examples:
                Example1:
                  value:
                    successes:
                      - id: abcdef-1234-5678-90ab
                        status: 201
                    errors: []
                Example2:
                  value:
                    successes:
                      - id: abcdef-1234-5678-90ab
                        status: 201
                    errors: []
                Example3:
                  value:
                    successes:
                      - id: abcdef-1234-5678-90ab
                        status: 201
                    errors: []
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                batch:
                  type: array
                  items:
                    $ref: '#/components/schemas/IngestionEvent'
                  description: >-
                    Batch of tracing events to be ingested. Discriminated by
                    attribute `type`.
                metadata:
                  nullable: true
                  description: >-
                    Optional. Metadata field used by the Langfuse SDKs for
                    debugging.
              required:
                - batch
            examples:
              Example1:
                value:
                  batch:
                    - id: abcdef-1234-5678-90ab
                      timestamp: '2022-01-01T00:00:00.000Z'
                      type: trace-create
                      body:
                        id: abcdef-1234-5678-90ab
                        timestamp: '2022-01-01T00:00:00.000Z'
                        environment: production
                        name: My Trace
                        userId: 1234-5678-90ab-cdef
                        input: My input
                        output: My output
                        sessionId: 1234-5678-90ab-cdef
                        release: 1.0.0
                        version: 1.0.0
                        metadata: My metadata
                        tags:
                          - tag1
                          - tag2
                        public: true
              Example2:
                value:
                  batch:
                    - id: abcdef-1234-5678-90ab
                      timestamp: '2022-01-01T00:00:00.000Z'
                      type: span-create
                      body:
                        id: abcdef-1234-5678-90ab
                        traceId: 1234-5678-90ab-cdef
                        startTime: '2022-01-01T00:00:00.000Z'
                        environment: test
              Example3:
                value:
                  batch:
                    - id: abcdef-1234-5678-90ab
                      timestamp: '2022-01-01T00:00:00.000Z'
                      type: score-create
                      body:
                        id: abcdef-1234-5678-90ab
                        traceId: 1234-5678-90ab-cdef
                        name: My Score
                        value: 0.9
                        environment: default
  /api/public/media/{mediaId}:
    get:
      description: Get a media record
      operationId: media_get
      tags:
        - Media
      parameters:
        - name: mediaId
          in: path
          description: The unique langfuse identifier of a media record
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMediaResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    patch:
      description: Patch a media record
      operationId: media_patch
      tags:
        - Media
      parameters:
        - name: mediaId
          in: path
          description: The unique langfuse identifier of a media record
          required: true
          schema:
            type: string
      responses:
        '204':
          description: ''
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchMediaBody'
  /api/public/media:
    post:
      description: Get a presigned upload URL for a media record
      operationId: media_getUploadUrl
      tags:
        - Media
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMediaUploadUrlResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetMediaUploadUrlRequest'
  /api/public/metrics:
    get:
      description: Get metrics from the Langfuse project using a query object
      operationId: metrics_metrics
      tags:
        - Metrics
      parameters:
        - name: query
          in: query
          description: >-
            JSON string containing the query parameters with the following
            structure:

            ```json

            {
              "view": string,           // Required. One of "traces", "observations", "scores-numeric", "scores-categorical"
              "dimensions": [           // Optional. Default: []
                {
                  "field": string       // Field to group by, e.g. "name", "userId", "sessionId"
                }
              ],
              "metrics": [              // Required. At least one metric must be provided
                {
                  "measure": string,    // What to measure, e.g. "count", "latency", "value"
                  "aggregation": string // How to aggregate, e.g. "count", "sum", "avg", "p95", "histogram"
                }
              ],
              "filters": [              // Optional. Default: []
                {
                  "column": string,     // Column to filter on
                  "operator": string,   // Operator, e.g. "=", ">", "<", "contains"
                  "value": any,         // Value to compare against
                  "type": string,       // Data type, e.g. "string", "number", "stringObject"
                  "key": string         // Required only when filtering on metadata
                }
              ],
              "timeDimension": {        // Optional. Default: null. If provided, results will be grouped by time
                "granularity": string   // One of "minute", "hour", "day", "week", "month", "auto"
              },
              "fromTimestamp": string,  // Required. ISO datetime string for start of time range
              "toTimestamp": string,    // Required. ISO datetime string for end of time range
              "orderBy": [              // Optional. Default: null
                {
                  "field": string,      // Field to order by
                  "direction": string   // "asc" or "desc"
                }
              ],
              "config": {               // Optional. Query-specific configuration
                "bins": number,         // Optional. Number of bins for histogram (1-100), default: 10
                "row_limit": number     // Optional. Row limit for results (1-1000)
              }
            }

            ```
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MetricsResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/models:
    post:
      description: Create a model
      operationId: models_create
      tags:
        - Models
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Model'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateModelRequest'
    get:
      description: Get all models
      operationId: models_list
      tags:
        - Models
      parameters:
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedModels'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/models/{id}:
    get:
      description: Get a model
      operationId: models_get
      tags:
        - Models
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Model'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: >-
        Delete a model. Cannot delete models managed by Langfuse. You can create
        your own definition with the same modelName to override the definition
        though.
      operationId: models_delete
      tags:
        - Models
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: ''
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/observations/{observationId}:
    get:
      description: Get a observation
      operationId: observations_get
      tags:
        - Observations
      parameters:
        - name: observationId
          in: path
          description: >-
            The unique langfuse identifier of an observation, can be an event,
            span or generation
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObservationsView'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/observations:
    get:
      description: Get a list of observations
      operationId: observations_getMany
      tags:
        - Observations
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1.
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit.
          required: false
          schema:
            type: integer
            nullable: true
        - name: name
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: userId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: type
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: traceId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: parentObservationId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: environment
          in: query
          description: >-
            Optional filter for observations where the environment is one of the
            provided values.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
        - name: fromStartTime
          in: query
          description: >-
            Retrieve only observations with a start_time on or after this
            datetime (ISO 8601).
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toStartTime
          in: query
          description: >-
            Retrieve only observations with a start_time before this datetime
            (ISO 8601).
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: version
          in: query
          description: Optional filter to only include observations with a certain version.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObservationsViews'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/organizations/memberships:
    get:
      description: >-
        Get all memberships for the organization associated with the API key
        (requires organization-scoped API key)
      operationId: organizations_getOrganizationMemberships
      tags:
        - Organizations
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipsResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    put:
      description: >-
        Create or update a membership for the organization associated with the
        API key (requires organization-scoped API key)
      operationId: organizations_updateOrganizationMembership
      tags:
        - Organizations
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MembershipRequest'
  /api/public/projects/{projectId}/memberships:
    get:
      description: >-
        Get all memberships for a specific project (requires organization-scoped
        API key)
      operationId: organizations_getProjectMemberships
      tags:
        - Organizations
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipsResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    put:
      description: >-
        Create or update a membership for a specific project (requires
        organization-scoped API key). The user must already be a member of the
        organization.
      operationId: organizations_updateProjectMembership
      tags:
        - Organizations
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MembershipResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MembershipRequest'
  /api/public/organizations/projects:
    get:
      description: >-
        Get all projects for the organization associated with the API key
        (requires organization-scoped API key)
      operationId: organizations_getOrganizationProjects
      tags:
        - Organizations
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationProjectsResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/projects:
    get:
      description: Get Project associated with API key
      operationId: projects_get
      tags:
        - Projects
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Projects'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: Create a new project (requires organization-scoped API key)
      operationId: projects_create
      tags:
        - Projects
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
                  nullable: true
                  description: Optional metadata for the project
                retention:
                  type: integer
                  description: >-
                    Number of days to retain data. Must be 0 or at least 3 days.
                    Requires data-retention entitlement for non-zero values.
                    Optional.
              required:
                - name
                - retention
  /api/public/projects/{projectId}:
    put:
      description: Update a project by ID (requires organization-scoped API key).
      operationId: projects_update
      tags:
        - Projects
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                metadata:
                  type: object
                  additionalProperties: true
                  nullable: true
                  description: Optional metadata for the project
                retention:
                  type: integer
                  description: >-
                    Number of days to retain data. Must be 0 or at least 3 days.
                    Requires data-retention entitlement for non-zero values.
                    Optional.
              required:
                - name
                - retention
    delete:
      description: >-
        Delete a project by ID (requires organization-scoped API key). Project
        deletion is processed asynchronously.
      operationId: projects_delete
      tags:
        - Projects
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDeletionResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/projects/{projectId}/apiKeys:
    get:
      description: Get all API keys for a project (requires organization-scoped API key)
      operationId: projects_getApiKeys
      tags:
        - Projects
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyList'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: >-
        Create a new API key for a project (requires organization-scoped API
        key)
      operationId: projects_createApiKey
      tags:
        - Projects
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                note:
                  type: string
                  nullable: true
                  description: Optional note for the API key
  /api/public/projects/{projectId}/apiKeys/{apiKeyId}:
    delete:
      description: Delete an API key for a project (requires organization-scoped API key)
      operationId: projects_deleteApiKey
      tags:
        - Projects
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
        - name: apiKeyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyDeletionResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/v2/prompts/{name}/versions/{version}:
    patch:
      description: Update labels for a specific prompt version
      operationId: promptVersion_update
      tags:
        - PromptVersion
      parameters:
        - name: name
          in: path
          description: The name of the prompt
          required: true
          schema:
            type: string
        - name: version
          in: path
          description: Version of the prompt to update
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Prompt'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                newLabels:
                  type: array
                  items:
                    type: string
                  description: >-
                    New labels for the prompt version. Labels are unique across
                    versions. The "latest" label is reserved and managed by
                    Langfuse.
              required:
                - newLabels
  /api/public/v2/prompts/{promptName}:
    get:
      description: Get a prompt
      operationId: prompts_get
      tags:
        - Prompts
      parameters:
        - name: promptName
          in: path
          description: The name of the prompt
          required: true
          schema:
            type: string
        - name: version
          in: query
          description: Version of the prompt to be retrieved.
          required: false
          schema:
            type: integer
            nullable: true
        - name: label
          in: query
          description: >-
            Label of the prompt to be retrieved. Defaults to "production" if no
            label or version is set.
          required: false
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Prompt'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/v2/prompts:
    get:
      description: Get a list of prompt names with versions and labels
      operationId: prompts_list
      tags:
        - Prompts
      parameters:
        - name: name
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: label
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: tag
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: page
          in: query
          description: page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: limit of items per page
          required: false
          schema:
            type: integer
            nullable: true
        - name: fromUpdatedAt
          in: query
          description: >-
            Optional filter to only include prompt versions created/updated on
            or after a certain datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toUpdatedAt
          in: query
          description: >-
            Optional filter to only include prompt versions created/updated
            before a certain datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PromptMetaListResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: Create a new version for the prompt with the given `name`
      operationId: prompts_create
      tags:
        - Prompts
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Prompt'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePromptRequest'
  /api/public/scim/ServiceProviderConfig:
    get:
      description: >-
        Get SCIM Service Provider Configuration (requires organization-scoped
        API key)
      operationId: scim_getServiceProviderConfig
      tags:
        - Scim
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceProviderConfig'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/scim/ResourceTypes:
    get:
      description: Get SCIM Resource Types (requires organization-scoped API key)
      operationId: scim_getResourceTypes
      tags:
        - Scim
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceTypesResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/scim/Schemas:
    get:
      description: Get SCIM Schemas (requires organization-scoped API key)
      operationId: scim_getSchemas
      tags:
        - Scim
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemasResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/scim/Users:
    get:
      description: List users in the organization (requires organization-scoped API key)
      operationId: scim_listUsers
      tags:
        - Scim
      parameters:
        - name: filter
          in: query
          description: Filter expression (e.g. userName eq "value")
          required: false
          schema:
            type: string
            nullable: true
        - name: startIndex
          in: query
          description: 1-based index of the first result to return (default 1)
          required: false
          schema:
            type: integer
            nullable: true
        - name: count
          in: query
          description: Maximum number of results to return (default 100)
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScimUsersListResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    post:
      description: >-
        Create a new user in the organization (requires organization-scoped API
        key)
      operationId: scim_createUser
      tags:
        - Scim
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScimUser'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email address (required)
                name:
                  $ref: '#/components/schemas/ScimName'
                  description: User's name information
                emails:
                  type: array
                  items:
                    $ref: '#/components/schemas/ScimEmail'
                  nullable: true
                  description: User's email addresses
                active:
                  type: boolean
                  nullable: true
                  description: Whether the user is active
                password:
                  type: string
                  nullable: true
                  description: Initial password for the user
              required:
                - userName
                - name
  /api/public/scim/Users/<USER>
    get:
      description: Get a specific user by ID (requires organization-scoped API key)
      operationId: scim_getUser
      tags:
        - Scim
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScimUser'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: >-
        Remove a user from the organization (requires organization-scoped API
        key). Note that this only removes the user from the organization but
        does not delete the user entity itself.
      operationId: scim_deleteUser
      tags:
        - Scim
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/score-configs:
    post:
      description: >-
        Create a score configuration (config). Score configs are used to define
        the structure of scores
      operationId: scoreConfigs_create
      tags:
        - ScoreConfigs
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoreConfig'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScoreConfigRequest'
    get:
      description: Get all score configs
      operationId: scoreConfigs_get
      tags:
        - ScoreConfigs
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1.
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit
          required: false
          schema:
            type: integer
            nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoreConfigs'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/score-configs/{configId}:
    get:
      description: Get a score config
      operationId: scoreConfigs_get-by-id
      tags:
        - ScoreConfigs
      parameters:
        - name: configId
          in: path
          description: The unique langfuse identifier of a score config
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoreConfig'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/v2/scores:
    get:
      description: Get a list of scores (supports both trace and session scores)
      operationId: scoreV2_get
      tags:
        - ScoreV2
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1.
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit.
          required: false
          schema:
            type: integer
            nullable: true
        - name: userId
          in: query
          description: Retrieve only scores with this userId associated to the trace.
          required: false
          schema:
            type: string
            nullable: true
        - name: name
          in: query
          description: Retrieve only scores with this name.
          required: false
          schema:
            type: string
            nullable: true
        - name: fromTimestamp
          in: query
          description: >-
            Optional filter to only include scores created on or after a certain
            datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toTimestamp
          in: query
          description: >-
            Optional filter to only include scores created before a certain
            datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: environment
          in: query
          description: >-
            Optional filter for scores where the environment is one of the
            provided values.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
        - name: source
          in: query
          description: Retrieve only scores from a specific source.
          required: false
          schema:
            $ref: '#/components/schemas/ScoreSource'
            nullable: true
        - name: operator
          in: query
          description: Retrieve only scores with <operator> value.
          required: false
          schema:
            type: string
            nullable: true
        - name: value
          in: query
          description: Retrieve only scores with <operator> value.
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: scoreIds
          in: query
          description: Comma-separated list of score IDs to limit the results to.
          required: false
          schema:
            type: string
            nullable: true
        - name: configId
          in: query
          description: Retrieve only scores with a specific configId.
          required: false
          schema:
            type: string
            nullable: true
        - name: queueId
          in: query
          description: Retrieve only scores with a specific annotation queueId.
          required: false
          schema:
            type: string
            nullable: true
        - name: dataType
          in: query
          description: Retrieve only scores with a specific dataType.
          required: false
          schema:
            $ref: '#/components/schemas/ScoreDataType'
            nullable: true
        - name: traceTags
          in: query
          description: >-
            Only scores linked to traces that include all of these tags will be
            returned.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetScoresResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/v2/scores/{scoreId}:
    get:
      description: Get a score (supports both trace and session scores)
      operationId: scoreV2_get-by-id
      tags:
        - ScoreV2
      parameters:
        - name: scoreId
          in: path
          description: The unique langfuse identifier of a score
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Score'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/scores:
    post:
      description: Create a score (supports both trace and session scores)
      operationId: score_create
      tags:
        - Score
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateScoreResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScoreRequest'
  /api/public/scores/{scoreId}:
    delete:
      description: Delete a score (supports both trace and session scores)
      operationId: score_delete
      tags:
        - Score
      parameters:
        - name: scoreId
          in: path
          description: The unique langfuse identifier of a score
          required: true
          schema:
            type: string
      responses:
        '204':
          description: ''
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/sessions:
    get:
      description: Get sessions
      operationId: sessions_list
      tags:
        - Sessions
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit.
          required: false
          schema:
            type: integer
            nullable: true
        - name: fromTimestamp
          in: query
          description: >-
            Optional filter to only include sessions created on or after a
            certain datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toTimestamp
          in: query
          description: >-
            Optional filter to only include sessions created before a certain
            datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: environment
          in: query
          description: >-
            Optional filter for sessions where the environment is one of the
            provided values.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSessions'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/sessions/{sessionId}:
    get:
      description: >-
        Get a session. Please note that `traces` on this endpoint are not
        paginated, if you plan to fetch large sessions, consider `GET
        /api/public/traces?sessionId=<sessionId>`
      operationId: sessions_get
      tags:
        - Sessions
      parameters:
        - name: sessionId
          in: path
          description: The unique id of a session
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionWithTraces'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/traces/{traceId}:
    get:
      description: Get a specific trace
      operationId: trace_get
      tags:
        - Trace
      parameters:
        - name: traceId
          in: path
          description: The unique langfuse identifier of a trace
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TraceWithFullDetails'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: Delete a specific trace
      operationId: trace_delete
      tags:
        - Trace
      parameters:
        - name: traceId
          in: path
          description: The unique langfuse identifier of the trace to delete
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTraceResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
  /api/public/traces:
    get:
      description: Get list of traces
      operationId: trace_list
      tags:
        - Trace
      parameters:
        - name: page
          in: query
          description: Page number, starts at 1
          required: false
          schema:
            type: integer
            nullable: true
        - name: limit
          in: query
          description: >-
            Limit of items per page. If you encounter api issues due to too
            large page sizes, try to reduce the limit.
          required: false
          schema:
            type: integer
            nullable: true
        - name: userId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: name
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: sessionId
          in: query
          required: false
          schema:
            type: string
            nullable: true
        - name: fromTimestamp
          in: query
          description: >-
            Optional filter to only include traces with a trace.timestamp on or
            after a certain datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: toTimestamp
          in: query
          description: >-
            Optional filter to only include traces with a trace.timestamp before
            a certain datetime (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
        - name: orderBy
          in: query
          description: >-
            Format of the string [field].[asc/desc]. Fields: id, timestamp,
            name, userId, release, version, public, bookmarked, sessionId.
            Example: timestamp.asc
          required: false
          schema:
            type: string
            nullable: true
        - name: tags
          in: query
          description: Only traces that include all of these tags will be returned.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
        - name: version
          in: query
          description: Optional filter to only include traces with a certain version.
          required: false
          schema:
            type: string
            nullable: true
        - name: release
          in: query
          description: Optional filter to only include traces with a certain release.
          required: false
          schema:
            type: string
            nullable: true
        - name: environment
          in: query
          description: >-
            Optional filter for traces where the environment is one of the
            provided values.
          required: false
          schema:
            type: array
            items:
              type: string
              nullable: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Traces'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
    delete:
      description: Delete multiple traces
      operationId: trace_deleteMultiple
      tags:
        - Trace
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTraceResponse'
        '400':
          description: ''
          content:
            application/json:
              schema: {}
        '401':
          description: ''
          content:
            application/json:
              schema: {}
        '403':
          description: ''
          content:
            application/json:
              schema: {}
        '404':
          description: ''
          content:
            application/json:
              schema: {}
        '405':
          description: ''
          content:
            application/json:
              schema: {}
      security:
        - BasicAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                traceIds:
                  type: array
                  items:
                    type: string
                  description: List of trace IDs to delete
              required:
                - traceIds
components:
  schemas:
    AnnotationQueueStatus:
      title: AnnotationQueueStatus
      type: string
      enum:
        - PENDING
        - COMPLETED
    AnnotationQueueObjectType:
      title: AnnotationQueueObjectType
      type: string
      enum:
        - TRACE
        - OBSERVATION
    AnnotationQueue:
      title: AnnotationQueue
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
          nullable: true
        scoreConfigIds:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - name
        - scoreConfigIds
        - createdAt
        - updatedAt
    AnnotationQueueItem:
      title: AnnotationQueueItem
      type: object
      properties:
        id:
          type: string
        queueId:
          type: string
        objectId:
          type: string
        objectType:
          $ref: '#/components/schemas/AnnotationQueueObjectType'
        status:
          $ref: '#/components/schemas/AnnotationQueueStatus'
        completedAt:
          type: string
          format: date-time
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - queueId
        - objectId
        - objectType
        - status
        - createdAt
        - updatedAt
    PaginatedAnnotationQueues:
      title: PaginatedAnnotationQueues
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AnnotationQueue'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    PaginatedAnnotationQueueItems:
      title: PaginatedAnnotationQueueItems
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AnnotationQueueItem'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateAnnotationQueueItemRequest:
      title: CreateAnnotationQueueItemRequest
      type: object
      properties:
        objectId:
          type: string
        objectType:
          $ref: '#/components/schemas/AnnotationQueueObjectType'
        status:
          $ref: '#/components/schemas/AnnotationQueueStatus'
          nullable: true
          description: Defaults to PENDING for new queue items
      required:
        - objectId
        - objectType
    UpdateAnnotationQueueItemRequest:
      title: UpdateAnnotationQueueItemRequest
      type: object
      properties:
        status:
          $ref: '#/components/schemas/AnnotationQueueStatus'
          nullable: true
    DeleteAnnotationQueueItemResponse:
      title: DeleteAnnotationQueueItemResponse
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
        - success
        - message
    CreateCommentRequest:
      title: CreateCommentRequest
      type: object
      properties:
        projectId:
          type: string
          description: The id of the project to attach the comment to.
        objectType:
          type: string
          description: >-
            The type of the object to attach the comment to (trace, observation,
            session, prompt).
        objectId:
          type: string
          description: >-
            The id of the object to attach the comment to. If this does not
            reference a valid existing object, an error will be thrown.
        content:
          type: string
          description: >-
            The content of the comment. May include markdown. Currently limited
            to 3000 characters.
        authorUserId:
          type: string
          nullable: true
          description: The id of the user who created the comment.
      required:
        - projectId
        - objectType
        - objectId
        - content
    CreateCommentResponse:
      title: CreateCommentResponse
      type: object
      properties:
        id:
          type: string
          description: The id of the created object in Langfuse
      required:
        - id
    GetCommentsResponse:
      title: GetCommentsResponse
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    Trace:
      title: Trace
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of a trace
        timestamp:
          type: string
          format: date-time
          description: The timestamp when the trace was created
        name:
          type: string
          nullable: true
          description: The name of the trace
        input:
          nullable: true
          description: The input data of the trace. Can be any JSON.
        output:
          nullable: true
          description: The output data of the trace. Can be any JSON.
        sessionId:
          type: string
          nullable: true
          description: The session identifier associated with the trace
        release:
          type: string
          nullable: true
          description: The release version of the application when the trace was created
        version:
          type: string
          nullable: true
          description: The version of the trace
        userId:
          type: string
          nullable: true
          description: The user identifier associated with the trace
        metadata:
          nullable: true
          description: The metadata associated with the trace. Can be any JSON.
        tags:
          type: array
          items:
            type: string
          nullable: true
          description: >-
            The tags associated with the trace. Can be an array of strings or
            null.
        public:
          type: boolean
          nullable: true
          description: Public traces are accessible via url without login
        environment:
          type: string
          nullable: true
          description: >-
            The environment from which this trace originated. Can be any
            lowercase alphanumeric string with hyphens and underscores that does
            not start with 'langfuse'.
      required:
        - id
        - timestamp
    TraceWithDetails:
      title: TraceWithDetails
      type: object
      properties:
        htmlPath:
          type: string
          description: Path of trace in Langfuse UI
        latency:
          type: number
          format: double
          description: Latency of trace in seconds
        totalCost:
          type: number
          format: double
          description: Cost of trace in USD
        observations:
          type: array
          items:
            type: string
          description: List of observation ids
        scores:
          type: array
          items:
            type: string
          description: List of score ids
      required:
        - htmlPath
        - latency
        - totalCost
        - observations
        - scores
      allOf:
        - $ref: '#/components/schemas/Trace'
    TraceWithFullDetails:
      title: TraceWithFullDetails
      type: object
      properties:
        htmlPath:
          type: string
          description: Path of trace in Langfuse UI
        latency:
          type: number
          format: double
          description: Latency of trace in seconds
        totalCost:
          type: number
          format: double
          description: Cost of trace in USD
        observations:
          type: array
          items:
            $ref: '#/components/schemas/ObservationsView'
          description: List of observations
        scores:
          type: array
          items:
            $ref: '#/components/schemas/ScoreV1'
          description: List of scores
      required:
        - htmlPath
        - latency
        - totalCost
        - observations
        - scores
      allOf:
        - $ref: '#/components/schemas/Trace'
    Session:
      title: Session
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        projectId:
          type: string
        environment:
          type: string
          nullable: true
          description: The environment from which this session originated.
      required:
        - id
        - createdAt
        - projectId
    SessionWithTraces:
      title: SessionWithTraces
      type: object
      properties:
        traces:
          type: array
          items:
            $ref: '#/components/schemas/Trace'
      required:
        - traces
      allOf:
        - $ref: '#/components/schemas/Session'
    Observation:
      title: Observation
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the observation
        traceId:
          type: string
          nullable: true
          description: The trace ID associated with the observation
        type:
          type: string
          description: The type of the observation
        name:
          type: string
          nullable: true
          description: The name of the observation
        startTime:
          type: string
          format: date-time
          description: The start time of the observation
        endTime:
          type: string
          format: date-time
          nullable: true
          description: The end time of the observation.
        completionStartTime:
          type: string
          format: date-time
          nullable: true
          description: The completion start time of the observation
        model:
          type: string
          nullable: true
          description: The model used for the observation
        modelParameters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/MapValue'
          nullable: true
          description: The parameters of the model used for the observation
        input:
          nullable: true
          description: The input data of the observation
        version:
          type: string
          nullable: true
          description: The version of the observation
        metadata:
          nullable: true
          description: Additional metadata of the observation
        output:
          nullable: true
          description: The output data of the observation
        usage:
          $ref: '#/components/schemas/Usage'
          nullable: true
          description: >-
            (Deprecated. Use usageDetails and costDetails instead.) The usage
            data of the observation
        level:
          $ref: '#/components/schemas/ObservationLevel'
          description: The level of the observation
        statusMessage:
          type: string
          nullable: true
          description: The status message of the observation
        parentObservationId:
          type: string
          nullable: true
          description: The parent observation ID
        promptId:
          type: string
          nullable: true
          description: The prompt ID associated with the observation
        usageDetails:
          type: object
          additionalProperties:
            type: integer
          nullable: true
          description: >-
            The usage details of the observation. Key is the name of the usage
            metric, value is the number of units consumed. The total key is the
            sum of all (non-total) usage metrics or the total value ingested.
        costDetails:
          type: object
          additionalProperties:
            type: number
            format: double
          nullable: true
          description: >-
            The cost details of the observation. Key is the name of the cost
            metric, value is the cost in USD. The total key is the sum of all
            (non-total) cost metrics or the total value ingested.
        environment:
          type: string
          nullable: true
          description: >-
            The environment from which this observation originated. Can be any
            lowercase alphanumeric string with hyphens and underscores that does
            not start with 'langfuse'.
      required:
        - id
        - type
        - startTime
        - level
    ObservationsView:
      title: ObservationsView
      type: object
      properties:
        promptName:
          type: string
          nullable: true
          description: The name of the prompt associated with the observation
        promptVersion:
          type: integer
          nullable: true
          description: The version of the prompt associated with the observation
        modelId:
          type: string
          nullable: true
          description: The unique identifier of the model
        inputPrice:
          type: number
          format: double
          nullable: true
          description: The price of the input in USD
        outputPrice:
          type: number
          format: double
          nullable: true
          description: The price of the output in USD.
        totalPrice:
          type: number
          format: double
          nullable: true
          description: The total price in USD.
        calculatedInputCost:
          type: number
          format: double
          nullable: true
          description: >-
            (Deprecated. Use usageDetails and costDetails instead.) The
            calculated cost of the input in USD
        calculatedOutputCost:
          type: number
          format: double
          nullable: true
          description: >-
            (Deprecated. Use usageDetails and costDetails instead.) The
            calculated cost of the output in USD
        calculatedTotalCost:
          type: number
          format: double
          nullable: true
          description: >-
            (Deprecated. Use usageDetails and costDetails instead.) The
            calculated total cost in USD
        latency:
          type: number
          format: double
          nullable: true
          description: The latency in seconds.
        timeToFirstToken:
          type: number
          format: double
          nullable: true
          description: The time to the first token in seconds
      allOf:
        - $ref: '#/components/schemas/Observation'
    Usage:
      title: Usage
      type: object
      description: >-
        (Deprecated. Use usageDetails and costDetails instead.) Standard
        interface for usage and cost
      properties:
        input:
          type: integer
          nullable: true
          description: Number of input units (e.g. tokens)
        output:
          type: integer
          nullable: true
          description: Number of output units (e.g. tokens)
        total:
          type: integer
          nullable: true
          description: Defaults to input+output if not set
        unit:
          $ref: '#/components/schemas/ModelUsageUnit'
          nullable: true
        inputCost:
          type: number
          format: double
          nullable: true
          description: USD input cost
        outputCost:
          type: number
          format: double
          nullable: true
          description: USD output cost
        totalCost:
          type: number
          format: double
          nullable: true
          description: USD total cost, defaults to input+output
    ScoreConfig:
      title: ScoreConfig
      type: object
      description: Configuration for a score
      properties:
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        projectId:
          type: string
        dataType:
          $ref: '#/components/schemas/ScoreDataType'
        isArchived:
          type: boolean
          description: Whether the score config is archived. Defaults to false
        minValue:
          type: number
          format: double
          nullable: true
          description: >-
            Sets minimum value for numerical scores. If not set, the minimum
            value defaults to -∞
        maxValue:
          type: number
          format: double
          nullable: true
          description: >-
            Sets maximum value for numerical scores. If not set, the maximum
            value defaults to +∞
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ConfigCategory'
          nullable: true
          description: Configures custom categories for categorical scores
        description:
          type: string
          nullable: true
      required:
        - id
        - name
        - createdAt
        - updatedAt
        - projectId
        - dataType
        - isArchived
    ConfigCategory:
      title: ConfigCategory
      type: object
      properties:
        value:
          type: number
          format: double
        label:
          type: string
      required:
        - value
        - label
    BaseScoreV1:
      title: BaseScoreV1
      type: object
      properties:
        id:
          type: string
        traceId:
          type: string
        name:
          type: string
        source:
          $ref: '#/components/schemas/ScoreSource'
        observationId:
          type: string
          nullable: true
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        authorUserId:
          type: string
          nullable: true
        comment:
          type: string
          nullable: true
        metadata:
          nullable: true
        configId:
          type: string
          nullable: true
          description: >-
            Reference a score config on a score. When set, config and score name
            must be equal and value must comply to optionally defined numerical
            range
        queueId:
          type: string
          nullable: true
          description: >-
            Reference an annotation queue on a score. Populated if the score was
            initially created in an annotation queue.
        environment:
          type: string
          nullable: true
          description: >-
            The environment from which this score originated. Can be any
            lowercase alphanumeric string with hyphens and underscores that does
            not start with 'langfuse'.
      required:
        - id
        - traceId
        - name
        - source
        - timestamp
        - createdAt
        - updatedAt
    NumericScoreV1:
      title: NumericScoreV1
      type: object
      properties:
        value:
          type: number
          format: double
          description: The numeric value of the score
      required:
        - value
      allOf:
        - $ref: '#/components/schemas/BaseScoreV1'
    BooleanScoreV1:
      title: BooleanScoreV1
      type: object
      properties:
        value:
          type: number
          format: double
          description: >-
            The numeric value of the score. Equals 1 for "True" and 0 for
            "False"
        stringValue:
          type: string
          description: >-
            The string representation of the score value. Is inferred from the
            numeric value and equals "True" or "False"
      required:
        - value
        - stringValue
      allOf:
        - $ref: '#/components/schemas/BaseScoreV1'
    CategoricalScoreV1:
      title: CategoricalScoreV1
      type: object
      properties:
        value:
          type: number
          format: double
          nullable: true
          description: >-
            Only defined if a config is linked. Represents the numeric category
            mapping of the stringValue
        stringValue:
          type: string
          description: >-
            The string representation of the score value. If no config is
            linked, can be any string. Otherwise, must map to a config category
      required:
        - stringValue
      allOf:
        - $ref: '#/components/schemas/BaseScoreV1'
    ScoreV1:
      title: ScoreV1
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - NUMERIC
            - $ref: '#/components/schemas/NumericScoreV1'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - CATEGORICAL
            - $ref: '#/components/schemas/CategoricalScoreV1'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - BOOLEAN
            - $ref: '#/components/schemas/BooleanScoreV1'
          required:
            - dataType
    BaseScore:
      title: BaseScore
      type: object
      properties:
        id:
          type: string
        traceId:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        observationId:
          type: string
          nullable: true
        datasetRunId:
          type: string
          nullable: true
        name:
          type: string
        source:
          $ref: '#/components/schemas/ScoreSource'
        timestamp:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        authorUserId:
          type: string
          nullable: true
        comment:
          type: string
          nullable: true
        metadata:
          nullable: true
        configId:
          type: string
          nullable: true
          description: >-
            Reference a score config on a score. When set, config and score name
            must be equal and value must comply to optionally defined numerical
            range
        queueId:
          type: string
          nullable: true
          description: >-
            Reference an annotation queue on a score. Populated if the score was
            initially created in an annotation queue.
        environment:
          type: string
          nullable: true
          description: >-
            The environment from which this score originated. Can be any
            lowercase alphanumeric string with hyphens and underscores that does
            not start with 'langfuse'.
      required:
        - id
        - name
        - source
        - timestamp
        - createdAt
        - updatedAt
    NumericScore:
      title: NumericScore
      type: object
      properties:
        value:
          type: number
          format: double
          description: The numeric value of the score
      required:
        - value
      allOf:
        - $ref: '#/components/schemas/BaseScore'
    BooleanScore:
      title: BooleanScore
      type: object
      properties:
        value:
          type: number
          format: double
          description: >-
            The numeric value of the score. Equals 1 for "True" and 0 for
            "False"
        stringValue:
          type: string
          description: >-
            The string representation of the score value. Is inferred from the
            numeric value and equals "True" or "False"
      required:
        - value
        - stringValue
      allOf:
        - $ref: '#/components/schemas/BaseScore'
    CategoricalScore:
      title: CategoricalScore
      type: object
      properties:
        value:
          type: number
          format: double
          nullable: true
          description: >-
            Only defined if a config is linked. Represents the numeric category
            mapping of the stringValue
        stringValue:
          type: string
          description: >-
            The string representation of the score value. If no config is
            linked, can be any string. Otherwise, must map to a config category
      required:
        - stringValue
      allOf:
        - $ref: '#/components/schemas/BaseScore'
    Score:
      title: Score
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - NUMERIC
            - $ref: '#/components/schemas/NumericScore'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - CATEGORICAL
            - $ref: '#/components/schemas/CategoricalScore'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - BOOLEAN
            - $ref: '#/components/schemas/BooleanScore'
          required:
            - dataType
    CreateScoreValue:
      title: CreateScoreValue
      oneOf:
        - type: number
          format: double
        - type: string
      description: >-
        The value of the score. Must be passed as string for categorical scores,
        and numeric for boolean and numeric scores
    Comment:
      title: Comment
      type: object
      properties:
        id:
          type: string
        projectId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        objectType:
          $ref: '#/components/schemas/CommentObjectType'
        objectId:
          type: string
        content:
          type: string
        authorUserId:
          type: string
          nullable: true
      required:
        - id
        - projectId
        - createdAt
        - updatedAt
        - objectType
        - objectId
        - content
    Dataset:
      title: Dataset
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
          nullable: true
        metadata:
          nullable: true
        projectId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - name
        - projectId
        - createdAt
        - updatedAt
    DatasetItem:
      title: DatasetItem
      type: object
      properties:
        id:
          type: string
        status:
          $ref: '#/components/schemas/DatasetStatus'
        input:
          nullable: true
        expectedOutput:
          nullable: true
        metadata:
          nullable: true
        sourceTraceId:
          type: string
          nullable: true
        sourceObservationId:
          type: string
          nullable: true
        datasetId:
          type: string
        datasetName:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - status
        - datasetId
        - datasetName
        - createdAt
        - updatedAt
    DatasetRunItem:
      title: DatasetRunItem
      type: object
      properties:
        id:
          type: string
        datasetRunId:
          type: string
        datasetRunName:
          type: string
        datasetItemId:
          type: string
        traceId:
          type: string
        observationId:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - datasetRunId
        - datasetRunName
        - datasetItemId
        - traceId
        - createdAt
        - updatedAt
    DatasetRun:
      title: DatasetRun
      type: object
      properties:
        id:
          type: string
          description: Unique identifier of the dataset run
        name:
          type: string
          description: Name of the dataset run
        description:
          type: string
          nullable: true
          description: Description of the run
        metadata:
          nullable: true
          description: Metadata of the dataset run
        datasetId:
          type: string
          description: Id of the associated dataset
        datasetName:
          type: string
          description: Name of the associated dataset
        createdAt:
          type: string
          format: date-time
          description: The date and time when the dataset run was created
        updatedAt:
          type: string
          format: date-time
          description: The date and time when the dataset run was last updated
      required:
        - id
        - name
        - datasetId
        - datasetName
        - createdAt
        - updatedAt
    DatasetRunWithItems:
      title: DatasetRunWithItems
      type: object
      properties:
        datasetRunItems:
          type: array
          items:
            $ref: '#/components/schemas/DatasetRunItem'
      required:
        - datasetRunItems
      allOf:
        - $ref: '#/components/schemas/DatasetRun'
    Model:
      title: Model
      type: object
      description: >-
        Model definition used for transforming usage into USD cost and/or
        tokenization.
      properties:
        id:
          type: string
        modelName:
          type: string
          description: >-
            Name of the model definition. If multiple with the same name exist,
            they are applied in the following order: (1) custom over built-in,
            (2) newest according to startTime where
            model.startTime<observation.startTime
        matchPattern:
          type: string
          description: >-
            Regex pattern which matches this model definition to
            generation.model. Useful in case of fine-tuned models. If you want
            to exact match, use `(?i)^modelname$`
        startDate:
          type: string
          format: date-time
          nullable: true
          description: Apply only to generations which are newer than this ISO date.
        unit:
          $ref: '#/components/schemas/ModelUsageUnit'
          nullable: true
          description: Unit used by this model.
        inputPrice:
          type: number
          format: double
          nullable: true
          description: Deprecated. See 'prices' instead. Price (USD) per input unit
        outputPrice:
          type: number
          format: double
          nullable: true
          description: Deprecated. See 'prices' instead. Price (USD) per output unit
        totalPrice:
          type: number
          format: double
          nullable: true
          description: >-
            Deprecated. See 'prices' instead. Price (USD) per total unit. Cannot
            be set if input or output price is set.
        tokenizerId:
          type: string
          nullable: true
          description: >-
            Optional. Tokenizer to be applied to observations which match to
            this model. See docs for more details.
        tokenizerConfig:
          nullable: true
          description: >-
            Optional. Configuration for the selected tokenizer. Needs to be
            JSON. See docs for more details.
        isLangfuseManaged:
          type: boolean
        prices:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ModelPrice'
          description: Price (USD) by usage type
      required:
        - id
        - modelName
        - matchPattern
        - isLangfuseManaged
        - prices
    ModelPrice:
      title: ModelPrice
      type: object
      properties:
        price:
          type: number
          format: double
      required:
        - price
    ModelUsageUnit:
      title: ModelUsageUnit
      type: string
      enum:
        - CHARACTERS
        - TOKENS
        - MILLISECONDS
        - SECONDS
        - IMAGES
        - REQUESTS
      description: Unit of usage in Langfuse
    ObservationLevel:
      title: ObservationLevel
      type: string
      enum:
        - DEBUG
        - DEFAULT
        - WARNING
        - ERROR
    MapValue:
      title: MapValue
      oneOf:
        - type: string
          nullable: true
        - type: integer
          nullable: true
        - type: boolean
          nullable: true
        - type: array
          items:
            type: string
          nullable: true
    CommentObjectType:
      title: CommentObjectType
      type: string
      enum:
        - TRACE
        - OBSERVATION
        - SESSION
        - PROMPT
    DatasetStatus:
      title: DatasetStatus
      type: string
      enum:
        - ACTIVE
        - ARCHIVED
    ScoreSource:
      title: ScoreSource
      type: string
      enum:
        - ANNOTATION
        - API
        - EVAL
    ScoreDataType:
      title: ScoreDataType
      type: string
      enum:
        - NUMERIC
        - BOOLEAN
        - CATEGORICAL
    DeleteDatasetItemResponse:
      title: DeleteDatasetItemResponse
      type: object
      properties:
        message:
          type: string
          description: Success message after deletion
      required:
        - message
    CreateDatasetItemRequest:
      title: CreateDatasetItemRequest
      type: object
      properties:
        datasetName:
          type: string
        input:
          nullable: true
        expectedOutput:
          nullable: true
        metadata:
          nullable: true
        sourceTraceId:
          type: string
          nullable: true
        sourceObservationId:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
          description: >-
            Dataset items are upserted on their id. Id needs to be unique
            (project-level) and cannot be reused across datasets.
        status:
          $ref: '#/components/schemas/DatasetStatus'
          nullable: true
          description: Defaults to ACTIVE for newly created items
      required:
        - datasetName
    PaginatedDatasetItems:
      title: PaginatedDatasetItems
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DatasetItem'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateDatasetRunItemRequest:
      title: CreateDatasetRunItemRequest
      type: object
      properties:
        runName:
          type: string
        runDescription:
          type: string
          nullable: true
          description: Description of the run. If run exists, description will be updated.
        metadata:
          nullable: true
          description: Metadata of the dataset run, updates run if run already exists
        datasetItemId:
          type: string
        observationId:
          type: string
          nullable: true
        traceId:
          type: string
          nullable: true
          description: >-
            traceId should always be provided. For compatibility with older SDK
            versions it can also be inferred from the provided observationId.
      required:
        - runName
        - datasetItemId
    PaginatedDatasetRunItems:
      title: PaginatedDatasetRunItems
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DatasetRunItem'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    PaginatedDatasets:
      title: PaginatedDatasets
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Dataset'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateDatasetRequest:
      title: CreateDatasetRequest
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        metadata:
          nullable: true
      required:
        - name
    PaginatedDatasetRuns:
      title: PaginatedDatasetRuns
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DatasetRun'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    DeleteDatasetRunResponse:
      title: DeleteDatasetRunResponse
      type: object
      properties:
        message:
          type: string
      required:
        - message
    HealthResponse:
      title: HealthResponse
      type: object
      properties:
        version:
          type: string
          description: Langfuse server version
          example: 1.25.0
        status:
          type: string
          example: OK
      required:
        - version
        - status
    IngestionEvent:
      title: IngestionEvent
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - trace-create
            - $ref: '#/components/schemas/TraceEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - score-create
            - $ref: '#/components/schemas/ScoreEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - span-create
            - $ref: '#/components/schemas/CreateSpanEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - span-update
            - $ref: '#/components/schemas/UpdateSpanEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - generation-create
            - $ref: '#/components/schemas/CreateGenerationEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - generation-update
            - $ref: '#/components/schemas/UpdateGenerationEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - event-create
            - $ref: '#/components/schemas/CreateEventEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - sdk-log
            - $ref: '#/components/schemas/SDKLogEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - observation-create
            - $ref: '#/components/schemas/CreateObservationEvent'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - observation-update
            - $ref: '#/components/schemas/UpdateObservationEvent'
          required:
            - type
    ObservationType:
      title: ObservationType
      type: string
      enum:
        - SPAN
        - GENERATION
        - EVENT
    IngestionUsage:
      title: IngestionUsage
      oneOf:
        - $ref: '#/components/schemas/Usage'
        - $ref: '#/components/schemas/OpenAIUsage'
    OpenAIUsage:
      title: OpenAIUsage
      type: object
      description: Usage interface of OpenAI for improved compatibility.
      properties:
        promptTokens:
          type: integer
          nullable: true
        completionTokens:
          type: integer
          nullable: true
        totalTokens:
          type: integer
          nullable: true
    OptionalObservationBody:
      title: OptionalObservationBody
      type: object
      properties:
        traceId:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        startTime:
          type: string
          format: date-time
          nullable: true
        metadata:
          nullable: true
        input:
          nullable: true
        output:
          nullable: true
        level:
          $ref: '#/components/schemas/ObservationLevel'
          nullable: true
        statusMessage:
          type: string
          nullable: true
        parentObservationId:
          type: string
          nullable: true
        version:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
    CreateEventBody:
      title: CreateEventBody
      type: object
      properties:
        id:
          type: string
          nullable: true
      allOf:
        - $ref: '#/components/schemas/OptionalObservationBody'
    UpdateEventBody:
      title: UpdateEventBody
      type: object
      properties:
        id:
          type: string
      required:
        - id
      allOf:
        - $ref: '#/components/schemas/OptionalObservationBody'
    CreateSpanBody:
      title: CreateSpanBody
      type: object
      properties:
        endTime:
          type: string
          format: date-time
          nullable: true
      allOf:
        - $ref: '#/components/schemas/CreateEventBody'
    UpdateSpanBody:
      title: UpdateSpanBody
      type: object
      properties:
        endTime:
          type: string
          format: date-time
          nullable: true
      allOf:
        - $ref: '#/components/schemas/UpdateEventBody'
    CreateGenerationBody:
      title: CreateGenerationBody
      type: object
      properties:
        completionStartTime:
          type: string
          format: date-time
          nullable: true
        model:
          type: string
          nullable: true
        modelParameters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/MapValue'
          nullable: true
        usage:
          $ref: '#/components/schemas/IngestionUsage'
          nullable: true
        usageDetails:
          $ref: '#/components/schemas/UsageDetails'
          nullable: true
        costDetails:
          type: object
          additionalProperties:
            type: number
            format: double
          nullable: true
        promptName:
          type: string
          nullable: true
        promptVersion:
          type: integer
          nullable: true
      allOf:
        - $ref: '#/components/schemas/CreateSpanBody'
    UpdateGenerationBody:
      title: UpdateGenerationBody
      type: object
      properties:
        completionStartTime:
          type: string
          format: date-time
          nullable: true
        model:
          type: string
          nullable: true
        modelParameters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/MapValue'
          nullable: true
        usage:
          $ref: '#/components/schemas/IngestionUsage'
          nullable: true
        promptName:
          type: string
          nullable: true
        usageDetails:
          $ref: '#/components/schemas/UsageDetails'
          nullable: true
        costDetails:
          type: object
          additionalProperties:
            type: number
            format: double
          nullable: true
        promptVersion:
          type: integer
          nullable: true
      allOf:
        - $ref: '#/components/schemas/UpdateSpanBody'
    ObservationBody:
      title: ObservationBody
      type: object
      properties:
        id:
          type: string
          nullable: true
        traceId:
          type: string
          nullable: true
        type:
          $ref: '#/components/schemas/ObservationType'
        name:
          type: string
          nullable: true
        startTime:
          type: string
          format: date-time
          nullable: true
        endTime:
          type: string
          format: date-time
          nullable: true
        completionStartTime:
          type: string
          format: date-time
          nullable: true
        model:
          type: string
          nullable: true
        modelParameters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/MapValue'
          nullable: true
        input:
          nullable: true
        version:
          type: string
          nullable: true
        metadata:
          nullable: true
        output:
          nullable: true
        usage:
          $ref: '#/components/schemas/Usage'
          nullable: true
        level:
          $ref: '#/components/schemas/ObservationLevel'
          nullable: true
        statusMessage:
          type: string
          nullable: true
        parentObservationId:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
      required:
        - type
    TraceBody:
      title: TraceBody
      type: object
      properties:
        id:
          type: string
          nullable: true
        timestamp:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          nullable: true
        userId:
          type: string
          nullable: true
        input:
          nullable: true
        output:
          nullable: true
        sessionId:
          type: string
          nullable: true
        release:
          type: string
          nullable: true
        version:
          type: string
          nullable: true
        metadata:
          nullable: true
        tags:
          type: array
          items:
            type: string
          nullable: true
        environment:
          type: string
          nullable: true
        public:
          type: boolean
          nullable: true
          description: Make trace publicly accessible via url
    SDKLogBody:
      title: SDKLogBody
      type: object
      properties:
        log: {}
      required:
        - log
    ScoreBody:
      title: ScoreBody
      type: object
      properties:
        id:
          type: string
          nullable: true
        traceId:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        observationId:
          type: string
          nullable: true
        datasetRunId:
          type: string
          nullable: true
        name:
          type: string
          example: novelty
        environment:
          type: string
          nullable: true
        value:
          $ref: '#/components/schemas/CreateScoreValue'
          description: >-
            The value of the score. Must be passed as string for categorical
            scores, and numeric for boolean and numeric scores. Boolean score
            values must equal either 1 or 0 (true or false)
        comment:
          type: string
          nullable: true
        metadata:
          nullable: true
        dataType:
          $ref: '#/components/schemas/ScoreDataType'
          nullable: true
          description: >-
            When set, must match the score value's type. If not set, will be
            inferred from the score value or config
        configId:
          type: string
          nullable: true
          description: >-
            Reference a score config on a score. When set, the score name must
            equal the config name and scores must comply with the config's range
            and data type. For categorical scores, the value must map to a
            config category. Numeric scores might be constrained by the score
            config's max and min values
      required:
        - name
        - value
    BaseEvent:
      title: BaseEvent
      type: object
      properties:
        id:
          type: string
          description: UUID v4 that identifies the event
        timestamp:
          type: string
          description: >-
            Datetime (ISO 8601) of event creation in client. Should be as close
            to actual event creation in client as possible, this timestamp will
            be used for ordering of events in future release. Resolution:
            milliseconds (required), microseconds (optimal).
        metadata:
          nullable: true
          description: Optional. Metadata field used by the Langfuse SDKs for debugging.
      required:
        - id
        - timestamp
    TraceEvent:
      title: TraceEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/TraceBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    CreateObservationEvent:
      title: CreateObservationEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/ObservationBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    UpdateObservationEvent:
      title: UpdateObservationEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/ObservationBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    ScoreEvent:
      title: ScoreEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/ScoreBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    SDKLogEvent:
      title: SDKLogEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/SDKLogBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    CreateGenerationEvent:
      title: CreateGenerationEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/CreateGenerationBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    UpdateGenerationEvent:
      title: UpdateGenerationEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/UpdateGenerationBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    CreateSpanEvent:
      title: CreateSpanEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/CreateSpanBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    UpdateSpanEvent:
      title: UpdateSpanEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/UpdateSpanBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    CreateEventEvent:
      title: CreateEventEvent
      type: object
      properties:
        body:
          $ref: '#/components/schemas/CreateEventBody'
      required:
        - body
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
    IngestionSuccess:
      title: IngestionSuccess
      type: object
      properties:
        id:
          type: string
        status:
          type: integer
      required:
        - id
        - status
    IngestionError:
      title: IngestionError
      type: object
      properties:
        id:
          type: string
        status:
          type: integer
        message:
          type: string
          nullable: true
        error:
          nullable: true
      required:
        - id
        - status
    IngestionResponse:
      title: IngestionResponse
      type: object
      properties:
        successes:
          type: array
          items:
            $ref: '#/components/schemas/IngestionSuccess'
        errors:
          type: array
          items:
            $ref: '#/components/schemas/IngestionError'
      required:
        - successes
        - errors
    OpenAICompletionUsageSchema:
      title: OpenAICompletionUsageSchema
      type: object
      description: OpenAI Usage schema from (Chat-)Completion APIs
      properties:
        prompt_tokens:
          type: integer
        completion_tokens:
          type: integer
        total_tokens:
          type: integer
        prompt_tokens_details:
          type: object
          additionalProperties:
            type: integer
            nullable: true
          nullable: true
        completion_tokens_details:
          type: object
          additionalProperties:
            type: integer
            nullable: true
          nullable: true
      required:
        - prompt_tokens
        - completion_tokens
        - total_tokens
    OpenAIResponseUsageSchema:
      title: OpenAIResponseUsageSchema
      type: object
      description: OpenAI Usage schema from Response API
      properties:
        input_tokens:
          type: integer
        output_tokens:
          type: integer
        total_tokens:
          type: integer
        input_tokens_details:
          type: object
          additionalProperties:
            type: integer
            nullable: true
          nullable: true
        output_tokens_details:
          type: object
          additionalProperties:
            type: integer
            nullable: true
          nullable: true
      required:
        - input_tokens
        - output_tokens
        - total_tokens
    UsageDetails:
      title: UsageDetails
      oneOf:
        - type: object
          additionalProperties:
            type: integer
        - $ref: '#/components/schemas/OpenAICompletionUsageSchema'
        - $ref: '#/components/schemas/OpenAIResponseUsageSchema'
    GetMediaResponse:
      title: GetMediaResponse
      type: object
      properties:
        mediaId:
          type: string
          description: The unique langfuse identifier of a media record
        contentType:
          type: string
          description: The MIME type of the media record
        contentLength:
          type: integer
          description: The size of the media record in bytes
        uploadedAt:
          type: string
          format: date-time
          description: The date and time when the media record was uploaded
        url:
          type: string
          description: The download URL of the media record
        urlExpiry:
          type: string
          description: The expiry date and time of the media record download URL
      required:
        - mediaId
        - contentType
        - contentLength
        - uploadedAt
        - url
        - urlExpiry
    PatchMediaBody:
      title: PatchMediaBody
      type: object
      properties:
        uploadedAt:
          type: string
          format: date-time
          description: The date and time when the media record was uploaded
        uploadHttpStatus:
          type: integer
          description: The HTTP status code of the upload
        uploadHttpError:
          type: string
          nullable: true
          description: The HTTP error message of the upload
        uploadTimeMs:
          type: integer
          nullable: true
          description: The time in milliseconds it took to upload the media record
      required:
        - uploadedAt
        - uploadHttpStatus
    GetMediaUploadUrlRequest:
      title: GetMediaUploadUrlRequest
      type: object
      properties:
        traceId:
          type: string
          description: The trace ID associated with the media record
        observationId:
          type: string
          nullable: true
          description: >-
            The observation ID associated with the media record. If the media
            record is associated directly with a trace, this will be null.
        contentType:
          $ref: '#/components/schemas/MediaContentType'
        contentLength:
          type: integer
          description: The size of the media record in bytes
        sha256Hash:
          type: string
          description: The SHA-256 hash of the media record
        field:
          type: string
          description: >-
            The trace / observation field the media record is associated with.
            This can be one of `input`, `output`, `metadata`
      required:
        - traceId
        - contentType
        - contentLength
        - sha256Hash
        - field
    GetMediaUploadUrlResponse:
      title: GetMediaUploadUrlResponse
      type: object
      properties:
        uploadUrl:
          type: string
          nullable: true
          description: >-
            The presigned upload URL. If the asset is already uploaded, this
            will be null
        mediaId:
          type: string
          description: The unique langfuse identifier of a media record
      required:
        - mediaId
    MediaContentType:
      title: MediaContentType
      type: string
      enum:
        - image/png
        - image/jpeg
        - image/jpg
        - image/webp
        - image/gif
        - image/svg+xml
        - image/tiff
        - image/bmp
        - audio/mpeg
        - audio/mp3
        - audio/wav
        - audio/ogg
        - audio/oga
        - audio/aac
        - audio/mp4
        - audio/flac
        - video/mp4
        - video/webm
        - text/plain
        - text/html
        - text/css
        - text/csv
        - application/pdf
        - application/msword
        - application/vnd.ms-excel
        - application/zip
        - application/json
        - application/xml
        - application/octet-stream
      description: The MIME type of the media record
    MetricsResponse:
      title: MetricsResponse
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            additionalProperties: true
          description: >-
            The metrics data. Each item in the list contains the metric values
            and dimensions requested in the query.

            Format varies based on the query parameters.

            Histograms will return an array with [lower, upper, height] tuples.
      required:
        - data
    PaginatedModels:
      title: PaginatedModels
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Model'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateModelRequest:
      title: CreateModelRequest
      type: object
      properties:
        modelName:
          type: string
          description: >-
            Name of the model definition. If multiple with the same name exist,
            they are applied in the following order: (1) custom over built-in,
            (2) newest according to startTime where
            model.startTime<observation.startTime
        matchPattern:
          type: string
          description: >-
            Regex pattern which matches this model definition to
            generation.model. Useful in case of fine-tuned models. If you want
            to exact match, use `(?i)^modelname$`
        startDate:
          type: string
          format: date-time
          nullable: true
          description: Apply only to generations which are newer than this ISO date.
        unit:
          $ref: '#/components/schemas/ModelUsageUnit'
          nullable: true
          description: Unit used by this model.
        inputPrice:
          type: number
          format: double
          nullable: true
          description: Price (USD) per input unit
        outputPrice:
          type: number
          format: double
          nullable: true
          description: Price (USD) per output unit
        totalPrice:
          type: number
          format: double
          nullable: true
          description: >-
            Price (USD) per total units. Cannot be set if input or output price
            is set.
        tokenizerId:
          type: string
          nullable: true
          description: >-
            Optional. Tokenizer to be applied to observations which match to
            this model. See docs for more details.
        tokenizerConfig:
          nullable: true
          description: >-
            Optional. Configuration for the selected tokenizer. Needs to be
            JSON. See docs for more details.
      required:
        - modelName
        - matchPattern
    Observations:
      title: Observations
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Observation'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    ObservationsViews:
      title: ObservationsViews
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ObservationsView'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    MembershipRole:
      title: MembershipRole
      type: string
      enum:
        - OWNER
        - ADMIN
        - MEMBER
        - VIEWER
    MembershipRequest:
      title: MembershipRequest
      type: object
      properties:
        userId:
          type: string
        role:
          $ref: '#/components/schemas/MembershipRole'
      required:
        - userId
        - role
    MembershipResponse:
      title: MembershipResponse
      type: object
      properties:
        userId:
          type: string
        role:
          $ref: '#/components/schemas/MembershipRole'
        email:
          type: string
        name:
          type: string
      required:
        - userId
        - role
        - email
        - name
    MembershipsResponse:
      title: MembershipsResponse
      type: object
      properties:
        memberships:
          type: array
          items:
            $ref: '#/components/schemas/MembershipResponse'
      required:
        - memberships
    OrganizationProject:
      title: OrganizationProject
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        metadata:
          type: object
          additionalProperties: true
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - name
        - createdAt
        - updatedAt
    OrganizationProjectsResponse:
      title: OrganizationProjectsResponse
      type: object
      properties:
        projects:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationProject'
      required:
        - projects
    Projects:
      title: Projects
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Project'
      required:
        - data
    Project:
      title: Project
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        metadata:
          type: object
          additionalProperties: true
          description: Metadata for the project
        retentionDays:
          type: integer
          nullable: true
          description: >-
            Number of days to retain data. Null or 0 means no retention. Omitted
            if no retention is configured.
      required:
        - id
        - name
        - metadata
    ProjectDeletionResponse:
      title: ProjectDeletionResponse
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
        - success
        - message
    ApiKeyList:
      title: ApiKeyList
      type: object
      description: List of API keys for a project
      properties:
        apiKeys:
          type: array
          items:
            $ref: '#/components/schemas/ApiKeySummary'
      required:
        - apiKeys
    ApiKeySummary:
      title: ApiKeySummary
      type: object
      description: Summary of an API key
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
          nullable: true
        lastUsedAt:
          type: string
          format: date-time
          nullable: true
        note:
          type: string
          nullable: true
        publicKey:
          type: string
        displaySecretKey:
          type: string
      required:
        - id
        - createdAt
        - publicKey
        - displaySecretKey
    ApiKeyResponse:
      title: ApiKeyResponse
      type: object
      description: Response for API key creation
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        publicKey:
          type: string
        secretKey:
          type: string
        displaySecretKey:
          type: string
        note:
          type: string
          nullable: true
      required:
        - id
        - createdAt
        - publicKey
        - secretKey
        - displaySecretKey
    ApiKeyDeletionResponse:
      title: ApiKeyDeletionResponse
      type: object
      description: Response for API key deletion
      properties:
        success:
          type: boolean
      required:
        - success
    PromptMetaListResponse:
      title: PromptMetaListResponse
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PromptMeta'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    PromptMeta:
      title: PromptMeta
      type: object
      properties:
        name:
          type: string
        versions:
          type: array
          items:
            type: integer
        labels:
          type: array
          items:
            type: string
        tags:
          type: array
          items:
            type: string
        lastUpdatedAt:
          type: string
          format: date-time
        lastConfig:
          description: >-
            Config object of the most recent prompt version that matches the
            filters (if any are provided)
      required:
        - name
        - versions
        - labels
        - tags
        - lastUpdatedAt
        - lastConfig
    CreatePromptRequest:
      title: CreatePromptRequest
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - chat
            - $ref: '#/components/schemas/CreateChatPromptRequest'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - text
            - $ref: '#/components/schemas/CreateTextPromptRequest'
          required:
            - type
    CreateChatPromptRequest:
      title: CreateChatPromptRequest
      type: object
      properties:
        name:
          type: string
        prompt:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessage'
        config:
          nullable: true
        labels:
          type: array
          items:
            type: string
          nullable: true
          description: List of deployment labels of this prompt version.
        tags:
          type: array
          items:
            type: string
          nullable: true
          description: List of tags to apply to all versions of this prompt.
        commitMessage:
          type: string
          nullable: true
          description: Commit message for this prompt version.
      required:
        - name
        - prompt
    CreateTextPromptRequest:
      title: CreateTextPromptRequest
      type: object
      properties:
        name:
          type: string
        prompt:
          type: string
        config:
          nullable: true
        labels:
          type: array
          items:
            type: string
          nullable: true
          description: List of deployment labels of this prompt version.
        tags:
          type: array
          items:
            type: string
          nullable: true
          description: List of tags to apply to all versions of this prompt.
        commitMessage:
          type: string
          nullable: true
          description: Commit message for this prompt version.
      required:
        - name
        - prompt
    Prompt:
      title: Prompt
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - chat
            - $ref: '#/components/schemas/ChatPrompt'
          required:
            - type
        - type: object
          allOf:
            - type: object
              properties:
                type:
                  type: string
                  enum:
                    - text
            - $ref: '#/components/schemas/TextPrompt'
          required:
            - type
    BasePrompt:
      title: BasePrompt
      type: object
      properties:
        name:
          type: string
        version:
          type: integer
        config: {}
        labels:
          type: array
          items:
            type: string
          description: List of deployment labels of this prompt version.
        tags:
          type: array
          items:
            type: string
          description: >-
            List of tags. Used to filter via UI and API. The same across
            versions of a prompt.
        commitMessage:
          type: string
          nullable: true
          description: Commit message for this prompt version.
        resolutionGraph:
          type: object
          additionalProperties: true
          nullable: true
          description: >-
            The dependency resolution graph for the current prompt. Null if
            prompt has no dependencies.
      required:
        - name
        - version
        - config
        - labels
        - tags
    ChatMessage:
      title: ChatMessage
      type: object
      properties:
        role:
          type: string
        content:
          type: string
      required:
        - role
        - content
    TextPrompt:
      title: TextPrompt
      type: object
      properties:
        prompt:
          type: string
      required:
        - prompt
      allOf:
        - $ref: '#/components/schemas/BasePrompt'
    ChatPrompt:
      title: ChatPrompt
      type: object
      properties:
        prompt:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessage'
      required:
        - prompt
      allOf:
        - $ref: '#/components/schemas/BasePrompt'
    ServiceProviderConfig:
      title: ServiceProviderConfig
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
        documentationUri:
          type: string
        patch:
          $ref: '#/components/schemas/ScimFeatureSupport'
        bulk:
          $ref: '#/components/schemas/BulkConfig'
        filter:
          $ref: '#/components/schemas/FilterConfig'
        changePassword:
          $ref: '#/components/schemas/ScimFeatureSupport'
        sort:
          $ref: '#/components/schemas/ScimFeatureSupport'
        etag:
          $ref: '#/components/schemas/ScimFeatureSupport'
        authenticationSchemes:
          type: array
          items:
            $ref: '#/components/schemas/AuthenticationScheme'
        meta:
          $ref: '#/components/schemas/ResourceMeta'
      required:
        - schemas
        - documentationUri
        - patch
        - bulk
        - filter
        - changePassword
        - sort
        - etag
        - authenticationSchemes
        - meta
    ScimFeatureSupport:
      title: ScimFeatureSupport
      type: object
      properties:
        supported:
          type: boolean
      required:
        - supported
    BulkConfig:
      title: BulkConfig
      type: object
      properties:
        supported:
          type: boolean
        maxOperations:
          type: integer
        maxPayloadSize:
          type: integer
      required:
        - supported
        - maxOperations
        - maxPayloadSize
    FilterConfig:
      title: FilterConfig
      type: object
      properties:
        supported:
          type: boolean
        maxResults:
          type: integer
      required:
        - supported
        - maxResults
    ResourceMeta:
      title: ResourceMeta
      type: object
      properties:
        resourceType:
          type: string
        location:
          type: string
      required:
        - resourceType
        - location
    AuthenticationScheme:
      title: AuthenticationScheme
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        specUri:
          type: string
        type:
          type: string
        primary:
          type: boolean
      required:
        - name
        - description
        - specUri
        - type
        - primary
    ResourceTypesResponse:
      title: ResourceTypesResponse
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
        totalResults:
          type: integer
        Resources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceType'
      required:
        - schemas
        - totalResults
        - Resources
    ResourceType:
      title: ResourceType
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
          nullable: true
        id:
          type: string
        name:
          type: string
        endpoint:
          type: string
        description:
          type: string
        schema:
          type: string
        schemaExtensions:
          type: array
          items:
            $ref: '#/components/schemas/SchemaExtension'
        meta:
          $ref: '#/components/schemas/ResourceMeta'
      required:
        - id
        - name
        - endpoint
        - description
        - schema
        - schemaExtensions
        - meta
    SchemaExtension:
      title: SchemaExtension
      type: object
      properties:
        schema:
          type: string
        required:
          type: boolean
      required:
        - schema
        - required
    SchemasResponse:
      title: SchemasResponse
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
        totalResults:
          type: integer
        Resources:
          type: array
          items:
            $ref: '#/components/schemas/SchemaResource'
      required:
        - schemas
        - totalResults
        - Resources
    SchemaResource:
      title: SchemaResource
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        attributes:
          type: array
          items: {}
        meta:
          $ref: '#/components/schemas/ResourceMeta'
      required:
        - id
        - name
        - description
        - attributes
        - meta
    ScimUsersListResponse:
      title: ScimUsersListResponse
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
        totalResults:
          type: integer
        startIndex:
          type: integer
        itemsPerPage:
          type: integer
        Resources:
          type: array
          items:
            $ref: '#/components/schemas/ScimUser'
      required:
        - schemas
        - totalResults
        - startIndex
        - itemsPerPage
        - Resources
    ScimUser:
      title: ScimUser
      type: object
      properties:
        schemas:
          type: array
          items:
            type: string
        id:
          type: string
        userName:
          type: string
        name:
          $ref: '#/components/schemas/ScimName'
        emails:
          type: array
          items:
            $ref: '#/components/schemas/ScimEmail'
        meta:
          $ref: '#/components/schemas/UserMeta'
      required:
        - schemas
        - id
        - userName
        - name
        - emails
        - meta
    UserMeta:
      title: UserMeta
      type: object
      properties:
        resourceType:
          type: string
        created:
          type: string
          nullable: true
        lastModified:
          type: string
          nullable: true
      required:
        - resourceType
    ScimName:
      title: ScimName
      type: object
      properties:
        formatted:
          type: string
          nullable: true
    ScimEmail:
      title: ScimEmail
      type: object
      properties:
        primary:
          type: boolean
        value:
          type: string
        type:
          type: string
      required:
        - primary
        - value
        - type
    EmptyResponse:
      title: EmptyResponse
      type: object
      description: Empty response for 204 No Content responses
      properties: {}
    ScoreConfigs:
      title: ScoreConfigs
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ScoreConfig'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateScoreConfigRequest:
      title: CreateScoreConfigRequest
      type: object
      properties:
        name:
          type: string
        dataType:
          $ref: '#/components/schemas/ScoreDataType'
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ConfigCategory'
          nullable: true
          description: >-
            Configure custom categories for categorical scores. Pass a list of
            objects with `label` and `value` properties. Categories are
            autogenerated for boolean configs and cannot be passed
        minValue:
          type: number
          format: double
          nullable: true
          description: >-
            Configure a minimum value for numerical scores. If not set, the
            minimum value defaults to -∞
        maxValue:
          type: number
          format: double
          nullable: true
          description: >-
            Configure a maximum value for numerical scores. If not set, the
            maximum value defaults to +∞
        description:
          type: string
          nullable: true
          description: >-
            Description is shown across the Langfuse UI and can be used to e.g.
            explain the config categories in detail, why a numeric range was
            set, or provide additional context on config name or usage
      required:
        - name
        - dataType
    GetScoresResponseTraceData:
      title: GetScoresResponseTraceData
      type: object
      properties:
        userId:
          type: string
          nullable: true
          description: The user ID associated with the trace referenced by score
        tags:
          type: array
          items:
            type: string
          nullable: true
          description: A list of tags associated with the trace referenced by score
        environment:
          type: string
          nullable: true
          description: The environment of the trace referenced by score
    GetScoresResponseDataNumeric:
      title: GetScoresResponseDataNumeric
      type: object
      properties:
        trace:
          $ref: '#/components/schemas/GetScoresResponseTraceData'
          nullable: true
      allOf:
        - $ref: '#/components/schemas/NumericScore'
    GetScoresResponseDataCategorical:
      title: GetScoresResponseDataCategorical
      type: object
      properties:
        trace:
          $ref: '#/components/schemas/GetScoresResponseTraceData'
          nullable: true
      allOf:
        - $ref: '#/components/schemas/CategoricalScore'
    GetScoresResponseDataBoolean:
      title: GetScoresResponseDataBoolean
      type: object
      properties:
        trace:
          $ref: '#/components/schemas/GetScoresResponseTraceData'
          nullable: true
      allOf:
        - $ref: '#/components/schemas/BooleanScore'
    GetScoresResponseData:
      title: GetScoresResponseData
      oneOf:
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - NUMERIC
            - $ref: '#/components/schemas/GetScoresResponseDataNumeric'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - CATEGORICAL
            - $ref: '#/components/schemas/GetScoresResponseDataCategorical'
          required:
            - dataType
        - type: object
          allOf:
            - type: object
              properties:
                dataType:
                  type: string
                  enum:
                    - BOOLEAN
            - $ref: '#/components/schemas/GetScoresResponseDataBoolean'
          required:
            - dataType
    GetScoresResponse:
      title: GetScoresResponse
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetScoresResponseData'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    CreateScoreRequest:
      title: CreateScoreRequest
      type: object
      properties:
        id:
          type: string
          nullable: true
        traceId:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        observationId:
          type: string
          nullable: true
        datasetRunId:
          type: string
          nullable: true
        name:
          type: string
          example: novelty
        value:
          $ref: '#/components/schemas/CreateScoreValue'
          description: >-
            The value of the score. Must be passed as string for categorical
            scores, and numeric for boolean and numeric scores. Boolean score
            values must equal either 1 or 0 (true or false)
        comment:
          type: string
          nullable: true
        metadata:
          nullable: true
        environment:
          type: string
          nullable: true
          description: >-
            The environment of the score. Can be any lowercase alphanumeric
            string with hyphens and underscores that does not start with
            'langfuse'.
        dataType:
          $ref: '#/components/schemas/ScoreDataType'
          nullable: true
          description: >-
            The data type of the score. When passing a configId this field is
            inferred. Otherwise, this field must be passed or will default to
            numeric.
        configId:
          type: string
          nullable: true
          description: >-
            Reference a score config on a score. The unique langfuse identifier
            of a score config. When passing this field, the dataType and
            stringValue fields are automatically populated.
      required:
        - name
        - value
    CreateScoreResponse:
      title: CreateScoreResponse
      type: object
      properties:
        id:
          type: string
          description: The id of the created object in Langfuse
      required:
        - id
    PaginatedSessions:
      title: PaginatedSessions
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Session'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    Traces:
      title: Traces
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TraceWithDetails'
        meta:
          $ref: '#/components/schemas/utilsMetaResponse'
      required:
        - data
        - meta
    DeleteTraceResponse:
      title: DeleteTraceResponse
      type: object
      properties:
        message:
          type: string
      required:
        - message
    Sort:
      title: Sort
      type: object
      properties:
        id:
          type: string
      required:
        - id
    utilsMetaResponse:
      title: utilsMetaResponse
      type: object
      properties:
        page:
          type: integer
          description: current page number
        limit:
          type: integer
          description: number of items per page
        totalItems:
          type: integer
          description: number of total items given the current filters/selection (if any)
        totalPages:
          type: integer
          description: number of total pages given the current limit
      required:
        - page
        - limit
        - totalItems
        - totalPages
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic

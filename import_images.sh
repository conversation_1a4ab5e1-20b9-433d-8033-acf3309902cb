#!/bin/bash

# 在 x86_64 服务器上运行此脚本来导入镜像

echo "正在导入 Docker 镜像..."

# 检查 docker_images_x86_64 目录是否存在
if [ ! -d "docker_images_x86_64" ]; then
    echo "错误: docker_images_x86_64 目录不存在"
    echo "请确保已将镜像文件传输到此目录"
    exit 1
fi

# 导入所有镜像
echo "导入 langfuse-web..."
gunzip -c docker_images_x86_64/langfuse-web.tar.gz | docker load

echo "导入 langfuse-worker..."
gunzip -c docker_images_x86_64/langfuse-worker.tar.gz | docker load

echo "导入 clickhouse..."
gunzip -c docker_images_x86_64/clickhouse.tar.gz | docker load

echo "导入 minio..."
gunzip -c docker_images_x86_64/minio.tar.gz | docker load

echo "导入 redis..."
gunzip -c docker_images_x86_64/redis.tar.gz | docker load

echo "导入 postgres..."
gunzip -c docker_images_x86_64/postgres.tar.gz | docker load

echo "所有镜像导入完成！"
echo "现在可以运行 docker-compose up -d 启动服务"

# 显示导入的镜像
echo "已导入的镜像:"
docker images | grep -E "(langfuse|clickhouse|minio|redis|postgres)"

# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  pagination: ./utils/pagination.yml
  commons: ./commons.yml
service:
  auth: true
  base-path: /api/public
  endpoints:
    create:
      docs: Create a score (supports both trace and session scores)
      method: POST
      path: /scores
      request: CreateScoreRequest
      response: CreateScoreResponse
    delete:
      docs: Delete a score (supports both trace and session scores)
      method: DELETE
      path: /scores/{scoreId}
      path-parameters:
        scoreId:
          type: string
          docs: The unique langfuse identifier of a score
types:
  CreateScoreRequest:
    properties:
      id: optional<string>
      traceId: optional<string>
      sessionId: optional<string>
      observationId: optional<string>
      datasetRunId: optional<string>
      name: string
      value:
        type: commons.CreateScoreValue
        docs: The value of the score. Must be passed as string for categorical scores, and numeric for boolean and numeric scores. Boolean score values must equal either 1 or 0 (true or false)
      comment: optional<string>
      metadata: optional<unknown>
      environment:
        type: optional<string>
        docs: The environment of the score. Can be any lowercase alphanumeric string with hyphens and underscores that does not start with 'langfuse'.
      dataType:
        type: optional<commons.ScoreDataType>
        docs: The data type of the score. When passing a configId this field is inferred. Otherwise, this field must be passed or will default to numeric.
      configId:
        type: optional<string>
        docs: Reference a score config on a score. The unique langfuse identifier of a score config. When passing this field, the dataType and stringValue fields are automatically populated.
    examples:
      - value:
          name: "novelty"
          value: 0.9
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "consistency"
          value: 1.2
          dataType: "NUMERIC"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "accuracy"
          value: 0.9
          dataType: "NUMERIC"
          configId: "9203-4567-89ab-cdef"
          traceId: "cdef-1234-5678-90ab"
          environment: "test"
      - value:
          name: "toxicity"
          value: "not toxic"
          traceId: "cdef-1234-5678-90ab"
          environment: "production"
      - value:
          name: "correctness"
          value: "partially correct"
          dataType: "CATEGORICAL"
          configId: "1234-5678-90ab-cdef"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "hallucination"
          value: 0
          dataType: "BOOLEAN"
          traceId: "cdef-1234-5678-90ab"
      - value:
          name: "helpfulness"
          value: 1
          dataType: "BOOLEAN"
          configId: "1234-5678-90ab-cdef"
          traceId: "cdef-1234-5678-90ab"
  CreateScoreResponse:
    properties:
      id:
        type: string
        docs: The id of the created object in Langfuse

#!/bin/bash

# 创建导出目录
mkdir -p docker_images_x86_64

echo "正在拉取 x86_64 架构的镜像..."

# 拉取所有需要的镜像 (x86_64 架构)
docker pull --platform linux/amd64 langfuse/langfuse:3
docker pull --platform linux/amd64 langfuse/langfuse-worker:3
docker pull --platform linux/amd64 clickhouse/clickhouse-server:latest
docker pull --platform linux/amd64 minio/minio:latest
docker pull --platform linux/amd64 redis:7
docker pull --platform linux/amd64 postgres:latest

echo "正在导出镜像..."

# 导出镜像为 tar 文件
docker save langfuse/langfuse:3 | gzip > docker_images_x86_64/langfuse-web.tar.gz
docker save langfuse/langfuse-worker:3 | gzip > docker_images_x86_64/langfuse-worker.tar.gz
docker save clickhouse/clickhouse-server:latest | gzip > docker_images_x86_64/clickhouse.tar.gz
docker save minio/minio:latest | gzip > docker_images_x86_64/minio.tar.gz
docker save redis:7 | gzip > docker_images_x86_64/redis.tar.gz
docker save postgres:latest | gzip > docker_images_x86_64/postgres.tar.gz

echo "镜像导出完成！文件位于 docker_images_x86_64/ 目录"
echo "请将整个 docker_images_x86_64 目录传输到你的 x86_64 服务器"

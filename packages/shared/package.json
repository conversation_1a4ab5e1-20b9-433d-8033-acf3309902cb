{"name": "@langfuse/shared", "version": "1.0.0", "license": "MIT", "private": true, "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "engines": {"node": "20"}, "exports": {".": {"import": "./dist/src/index.js", "require": "./dist/src/index.js"}, "./src/db": {"import": "./dist/src/db.js", "require": "./dist/src/db.js"}, "./src/server": {"import": "./dist/src/server/index.js", "require": "./dist/src/server/index.js"}, "./src/server/auth/apiKeys": {"import": "./dist/src/server/auth/apiKeys.js", "require": "./dist/src/server/auth/apiKeys.js"}, "./encryption": {"import": "./dist/src/encryption/index.js", "require": "./dist/src/encryption/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 70", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "db:migrate": "DISABLE_ERD=false dotenv -e ../../.env -- npx prisma migrate dev", "db:push": "DISABLE_ERD=false dotenv -e ../../.env -- npx prisma db push", "db:reset": "dotenv -e ../../.env npx -- prisma migrate reset", "db:deploy": "dotenv -e ../../.env npx -- prisma migrate deploy", "db:seed": "dotenv -e ../../.env -- npx prisma db seed", "db:generate": "dotenv -e ../../.env -- npx prisma generate", "db:seed:examples": "dotenv -e ../../.env -- npx prisma db seed -- --environment examples", "db:seed:load": "dotenv -e ../../.env -- npx prisma db seed -- --environment load", "ch:up": "bash clickhouse/scripts/up.sh", "ch:down": "bash clickhouse/scripts/down.sh", "ch:drop": "bash clickhouse/scripts/drop.sh", "ch:reset": "pnpm run ch:down && pnpm run ch:up && pnpm run ch:seed", "ch:seed": "dotenv -e ../../.env -- ts-node -r tsconfig-paths/register -r dotenv/config --compiler-options '{\"module\":\"CommonJS\"}' clickhouse/scripts/seed.ts", "load:setup": "dotenv -e ../../.env -- tsx scripts/load-seed.ts"}, "prisma": {"seed": "ts-node -r tsconfig-paths/register -r dotenv/config --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@aws-sdk/client-cloudwatch": "^3.675.0", "@aws-sdk/client-s3": "^3.675.0", "@aws-sdk/lib-storage": "^3.675.0", "@aws-sdk/s3-request-presigner": "^3.679.0", "@azure/storage-blob": "^12.26.0", "@clickhouse/client": "^1.11.2", "@google-cloud/storage": "^7.15.2", "@langchain/anthropic": "^0.3.21", "@langchain/aws": "^0.1.10", "@langchain/core": "^0.3.57", "@langchain/google-genai": "^0.2.10", "@langchain/google-vertexai": "^0.2.10", "@langchain/openai": "^0.5.12", "@opentelemetry/api": ">=1.0.0 <1.10.0", "@prisma/client": "^6.3.0", "@react-email/components": "^0.0.42", "@react-email/render": "^1.1.2", "@types/bcryptjs": "^2.4.6", "axios": "^1.8.2", "bcryptjs": "^2.4.3", "bullmq": "^5.34.10", "dd-trace": "^5.36.0", "decimal.js": "^10.4.3", "exponential-backoff": "^3.1.1", "https-proxy-agent": "^7.0.6", "ioredis": "^5.4.1", "kysely": "^0.27.4", "langchain": "^0.3.27", "langfuse-langchain": "3.37.4", "lodash": "^4.17.21", "lossless-json": "^4.0.2", "next-auth": "^4.24.11", "nodemailer": "^6.9.15", "prisma-extension-kysely": "^2.1.0", "uuid": "^9.0.1", "winston": "^3.15.0", "zod": "^3.25.62", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.17.10", "@types/node": "^20.11.29", "@types/nodemailer": "^6.4.16", "@types/pg": "^8.11.10", "@types/react": "18.2.79", "@types/uuid": "^9.0.8", "@typescript-eslint/parser": "^7.12.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-prettier": "^5.1.3", "kysely-codegen": "^0.16.8", "nodemon": "^3.1.7", "prettier": "^3.3.3", "prisma": "^6.3.0", "prisma-erd-generator": "^1.11.2", "prisma-kysely": "^1.8.0", "ts-node": "^10.9.2", "tsc-watch": "^6.2.0", "tsx": "^4.19.1", "typescript": "^5.4.5", "vitest": "^2.1.2"}, "peerDependencies": {"@types/react": "~18.2.79", "react": "~18.2.0"}, "pnpm": {"overrides": {"nanoid": "^3.3.8"}}}
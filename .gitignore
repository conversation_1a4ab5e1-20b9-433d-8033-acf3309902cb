# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/certs/

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# idea
.idea

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# do not commit any .env files to git, except for the .env.example file. https://create.t3.gg/en/usage/env-variables#using-environment-variables
.env*
!.env.dev.example
!.env.dev-azure.example
!.env.dev-redis-cluster.example
!.env.prod.example

# vercel
.vercel

# typescript
*.tsbuildinfo

/generated/typescript-server

# openapi spec that is copied during build
/public/openapi*.yml


# vscode
.devcontainer

node_modules
**/node_modules
**/dist
**/.next/*
**/.turbo/*
.yarn
.turbo

web/test-results/*
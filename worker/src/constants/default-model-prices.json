[{"id": "b9854a5c92dc496b997d99d20", "model_name": "gpt-4o", "match_pattern": "(?i)^(gpt-4o)$", "created_at": "2024-05-13T23:15:07.670Z", "updated_at": "2024-12-03T10:12:31.000Z", "prices": {"input": 2.5e-06, "input_cached_tokens": 1.25e-06, "input_cache_read": 1.25e-06, "output": 1e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "b9854a5c92dc496b997d99d21", "model_name": "gpt-4o-2024-05-13", "match_pattern": "(?i)^(gpt-4o-2024-05-13)$", "created_at": "2024-05-13T23:15:07.670Z", "updated_at": "2024-05-13T23:15:07.670Z", "prices": {"input": 5e-06, "output": 1.5e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o-2024-05-13", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkvq6iq000008ju6c16gynt", "model_name": "gpt-4-1106-preview", "match_pattern": "(?i)^(gpt-4-1106-preview)$", "created_at": "2024-04-23T10:37:17.092Z", "updated_at": "2024-04-23T10:37:17.092Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-1106-preview", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkvx5gp000108juaogs54ea", "model_name": "gpt-4-turbo-vision", "match_pattern": "(?i)^(gpt-4(-\\d{4})?-vision-preview)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-vision-preview", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkvyzgw000308jue4hse4j9", "model_name": "gpt-4-32k", "match_pattern": "(?i)^(gpt-4-32k)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 6e-05, "output": 0.00012}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-32k", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkwk4cb000108l5hwwh3zdi", "model_name": "gpt-4-32k-0613", "match_pattern": "(?i)^(gpt-4-32k-0613)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 6e-05, "output": 0.00012}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-32k-0613", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkwk4cb000208l59yvb9yq8", "model_name": "gpt-3.5-turbo-1106", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-1106)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 1e-06, "output": 2e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-1106", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkwk4cc000808l51xmk4uic", "model_name": "gpt-3.5-turbo-0613", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-0613)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 1.5e-06, "output": 2e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-0613", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkwk4cc000908l537kl0rx3", "model_name": "gpt-4-0613", "match_pattern": "(?i)^(gpt-4-0613)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 3e-05, "output": 6e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-0613", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrkwk4cc000a08l562uc3s9g", "model_name": "gpt-3.5-turbo-instruct", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-instruct)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 1.5e-06, "output": 2e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrntjt89000108jwcou1af71", "model_name": "text-ada-001", "match_pattern": "(?i)^(text-ada-001)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 4e-06}, "tokenizer_config": {"tokenizerModel": "text-ada-001"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000208jwawjr894q", "model_name": "text-babbage-001", "match_pattern": "(?i)^(text-babbage-001)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 5e-07}, "tokenizer_config": {"tokenizerModel": "text-babbage-001"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000308jw0jtfa4rs", "model_name": "text-curie-001", "match_pattern": "(?i)^(text-curie-001)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 2e-05}, "tokenizer_config": {"tokenizerModel": "text-curie-001"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000408jwc2c93h6i", "model_name": "text-davinci-001", "match_pattern": "(?i)^(text-davinci-001)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 2e-05}, "tokenizer_config": {"tokenizerModel": "text-davinci-001"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000508jw192m64qi", "model_name": "text-davinci-002", "match_pattern": "(?i)^(text-davinci-002)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 2e-05}, "tokenizer_config": {"tokenizerModel": "text-davinci-002"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000608jw4m3x5s55", "model_name": "text-davinci-003", "match_pattern": "(?i)^(text-davinci-003)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 2e-05}, "tokenizer_config": {"tokenizerModel": "text-davinci-003"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000908jwhvkz5crg", "model_name": "text-embedding-ada-002-v2", "match_pattern": "(?i)^(text-embedding-ada-002-v2)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 1e-07}, "tokenizer_config": {"tokenizerModel": "text-embedding-ada-002"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000908jwhvkz5crm", "model_name": "text-embedding-ada-002", "match_pattern": "(?i)^(text-embedding-ada-002)$", "created_at": "2024-01-24T18:18:50.861Z", "updated_at": "2024-01-24T18:18:50.861Z", "prices": {"total": 1e-07}, "tokenizer_config": {"tokenizerModel": "text-embedding-ada-002"}, "tokenizer_id": "openai"}, {"id": "clrntjt89000a08jw0gcdbd5a", "model_name": "gpt-3.5-turbo-16k-0613", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-16k-0613)$", "created_at": "2024-02-03T17:29:57.350Z", "updated_at": "2024-02-03T17:29:57.350Z", "prices": {"input": 3e-06, "output": 4e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-16k-0613", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrntkjgy000a08jx4e062mr0", "model_name": "gpt-3.5-turbo-0301", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-0301)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 2e-06, "output": 2e-06}, "tokenizer_config": {"tokensPerName": -1, "tokenizerModel": "gpt-3.5-turbo-0301", "tokensPerMessage": 4}, "tokenizer_id": "openai"}, {"id": "clrntkjgy000d08jx0p4y9h4l", "model_name": "gpt-4-32k-0314", "match_pattern": "(?i)^(gpt-4-32k-0314)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 6e-05, "output": 0.00012}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-32k-0314", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrntkjgy000e08jx4x6uawoo", "model_name": "gpt-4-0314", "match_pattern": "(?i)^(gpt-4-0314)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 3e-05, "output": 6e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-0314", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrntkjgy000f08jx79v9g1xj", "model_name": "gpt-4", "match_pattern": "(?i)^(gpt-4)$", "created_at": "2024-01-24T10:19:21.693Z", "updated_at": "2024-01-24T10:19:21.693Z", "prices": {"input": 3e-05, "output": 6e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clrnwb41q000308jsfrac9uh6", "model_name": "claude-instant-1.2", "match_pattern": "(?i)^(claude-instant-1.2)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 1.63e-06, "output": 5.51e-06}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwb836000408jsallr6u11", "model_name": "claude-2.0", "match_pattern": "(?i)^(claude-2.0)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 8e-06, "output": 2.4e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwbd1m000508js4hxu6o7n", "model_name": "claude-2.1", "match_pattern": "(?i)^(claude-2.1)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 8e-06, "output": 2.4e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwbg2b000608jse2pp4q2d", "model_name": "claude-1.3", "match_pattern": "(?i)^(claude-1.3)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 8e-06, "output": 2.4e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwbi9d000708jseiy44k26", "model_name": "claude-1.2", "match_pattern": "(?i)^(claude-1.2)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 8e-06, "output": 2.4e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwblo0000808jsc1385hdp", "model_name": "claude-1.1", "match_pattern": "(?i)^(claude-1.1)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 8e-06, "output": 2.4e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrnwbota000908jsgg9mb1ml", "model_name": "claude-instant-1", "match_pattern": "(?i)^(claude-instant-1)$", "created_at": "2024-01-30T15:44:13.447Z", "updated_at": "2024-01-30T15:44:13.447Z", "prices": {"input": 1.63e-06, "output": 5.51e-06}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clrs2dnql000108l46vo0gp2t", "model_name": "babbage-002", "match_pattern": "(?i)^(babbage-002)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"input": 4e-07, "output": 1.6e-06}, "tokenizer_config": {"tokenizerModel": "babbage-002"}, "tokenizer_id": "openai"}, {"id": "clrs2ds35000208l4g4b0hi3u", "model_name": "davinci-002", "match_pattern": "(?i)^(davinci-002)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"input": 6e-06, "output": 1.2e-05}, "tokenizer_config": {"tokenizerModel": "davinci-002"}, "tokenizer_id": "openai"}, {"id": "clruwn3pc00010al7bl611c8o", "model_name": "text-embedding-3-small", "match_pattern": "(?i)^(text-embedding-3-small)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"total": 2e-08}, "tokenizer_config": {"tokenizerModel": "text-embedding-ada-002"}, "tokenizer_id": "openai"}, {"id": "clruwn76700020al7gp8e4g4l", "model_name": "text-embedding-3-large", "match_pattern": "(?i)^(text-embedding-3-large)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"total": 1.3e-07}, "tokenizer_config": {"tokenizerModel": "text-embedding-ada-002"}, "tokenizer_id": "openai"}, {"id": "clruwnahl00030al7ab9rark7", "model_name": "gpt-3.5-turbo-0125", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-0125)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"input": 5e-07, "output": 1.5e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clruwnahl00040al78f1lb0at", "model_name": "gpt-3.5-turbo", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo)$", "created_at": "2024-02-13T12:00:37.424Z", "updated_at": "2024-02-13T12:00:37.424Z", "prices": {"input": 5e-07, "output": 1.5e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clruwnahl00050al796ck3p44", "model_name": "gpt-4-0125-preview", "match_pattern": "(?i)^(gpt-4-0125-preview)$", "created_at": "2024-01-26T17:35:21.129Z", "updated_at": "2024-01-26T17:35:21.129Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cls08r8sq000308jq14ae96f0", "model_name": "ft:gpt-3.5-turbo-1106", "match_pattern": "(?i)^(ft:)(gpt-3.5-turbo-1106:)(.+)(:)(.*)(:)(.+)$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 3e-06, "output": 6e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-1106", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cls08rp99000408jqepxoakjv", "model_name": "ft:gpt-3.5-turbo-0613", "match_pattern": "(?i)^(ft:)(gpt-3.5-turbo-0613:)(.+)(:)(.*)(:)(.+)$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 1.2e-05, "output": 1.6e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-0613", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cls08rv9g000508jq5p4z4nlr", "model_name": "ft:davinci-002", "match_pattern": "(?i)^(ft:)(davinci-002:)(.+)(:)(.*)(:)(.+)$$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 1.2e-05, "output": 1.2e-05}, "tokenizer_config": {"tokenizerModel": "davinci-002"}, "tokenizer_id": "openai"}, {"id": "cls08s2bw000608jq57wj4un2", "model_name": "ft:babbage-002", "match_pattern": "(?i)^(ft:)(babbage-002:)(.+)(:)(.*)(:)(.+)$$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 1.6e-06, "output": 1.6e-06}, "tokenizer_config": {"tokenizerModel": "babbage-002"}, "tokenizer_id": "openai"}, {"id": "cls0iv12d000108l251gf3038", "model_name": "chat-bison", "match_pattern": "(?i)^(chat-bison)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0j33v1000008joagkc4lql", "model_name": "codechat-bison-32k", "match_pattern": "(?i)^(codechat-bison-32k)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0jmc9v000008l8ee6r3gsd", "model_name": "codechat-bison", "match_pattern": "(?i)^(codechat-bison)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0jmjt3000108l83ix86w0d", "model_name": "text-bison-32k", "match_pattern": "(?i)^(text-bison-32k)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0jni4t000008jk3kyy803r", "model_name": "chat-bison-32k", "match_pattern": "(?i)^(chat-bison-32k)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0jungb000208jk12gm4gk1", "model_name": "text-unicorn", "match_pattern": "(?i)^(text-unicorn)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-06, "output": 7.5e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls0juygp000308jk2a6x9my2", "model_name": "text-bison", "match_pattern": "(?i)^(text-bison)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls1nyj5q000208l33ne901d8", "model_name": "textembedding-gecko", "match_pattern": "(?i)^(textembedding-gecko)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"total": 1e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls1nyyjp000308l31gxy1bih", "model_name": "textembedding-gecko-multilingual", "match_pattern": "(?i)^(textembedding-gecko-multilingual)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"total": 1e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls1nzjt3000508l3dnwad3g0", "model_name": "code-gecko", "match_pattern": "(?i)^(code-gecko)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls1nzwx4000608l38va7e4tv", "model_name": "code-bison", "match_pattern": "(?i)^(code-bison)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cls1o053j000708l39f8g4bgs", "model_name": "code-bison-32k", "match_pattern": "(?i)^(code-bison-32k)(@[a-zA-Z0-9]+)?$", "created_at": "2024-01-31T13:25:02.141Z", "updated_at": "2024-01-31T13:25:02.141Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "clsk9lntu000008jwfc51bbqv", "model_name": "gpt-3.5-turbo-16k", "match_pattern": "(?i)^(gpt-)(35|3.5)(-turbo-16k)$", "created_at": "2024-02-13T12:00:37.424Z", "updated_at": "2024-02-13T12:00:37.424Z", "prices": {"input": 5e-07, "output": 1.5e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-3.5-turbo-16k", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clsnq07bn000008l4e46v1ll8", "model_name": "gpt-4-turbo-preview", "match_pattern": "(?i)^(gpt-4-turbo-preview)$", "created_at": "2024-02-15T21:21:50.947Z", "updated_at": "2024-02-15T21:21:50.947Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cltgy0iuw000008le3vod1hhy", "model_name": "claude-3-opus-20240229", "match_pattern": "(?i)^(claude-3-opus-20240229|anthropic\\.claude-3-opus-20240229-v1:0|claude-3-opus@20240229)$", "created_at": "2024-03-07T17:55:38.139Z", "updated_at": "2024-03-07T17:55:38.139Z", "prices": {"input": 1.5e-05, "output": 7.5e-05}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cltgy0pp6000108le56se7bl3", "model_name": "claude-3-sonnet-20240229", "match_pattern": "(?i)^(claude-3-sonnet-20240229|anthropic\\.claude-3-sonnet-20240229-v1:0|claude-3-sonnet@20240229)$", "created_at": "2024-03-07T17:55:38.139Z", "updated_at": "2024-12-03T12:20:04.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cltr0w45b000008k1407o9qv1", "model_name": "claude-3-haiku-20240307", "match_pattern": "(?i)^(claude-3-haiku-20240307|anthropic\\.claude-3-haiku-20240307-v1:0|claude-3-haiku@20240307)$", "created_at": "2024-03-14T09:41:18.736Z", "updated_at": "2024-03-14T09:41:18.736Z", "prices": {"input": 2.5e-07, "output": 1.25e-06}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cluv2sjeo000008ih0fv23hi0", "model_name": "gemini-1.0-pro-latest", "match_pattern": "(?i)^(gemini-1.0-pro-latest)(@[a-zA-Z0-9]+)?$", "created_at": "2024-04-11T10:27:46.517Z", "updated_at": "2024-04-11T10:27:46.517Z", "prices": {"input": 2.5e-07, "output": 5e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cluv2subq000108ih2mlrga6a", "model_name": "gemini-1.0-pro", "match_pattern": "(?i)^(gemini-1.0-pro)(@[a-zA-Z0-9]+)?$", "created_at": "2024-04-11T10:27:46.517Z", "updated_at": "2024-04-11T10:27:46.517Z", "prices": {"input": 1.25e-07, "output": 3.75e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cluv2sx04000208ihbek75lsz", "model_name": "gemini-1.0-pro-001", "match_pattern": "(?i)^(gemini-1.0-pro-001)(@[a-zA-Z0-9]+)?$", "created_at": "2024-04-11T10:27:46.517Z", "updated_at": "2024-04-11T10:27:46.517Z", "prices": {"input": 1.25e-07, "output": 3.75e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cluv2szw0000308ihch3n79x7", "model_name": "gemini-pro", "match_pattern": "(?i)^(gemini-pro)(@[a-zA-Z0-9]+)?$", "created_at": "2024-04-11T10:27:46.517Z", "updated_at": "2024-04-11T10:27:46.517Z", "prices": {"input": 1.25e-07, "output": 3.75e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cluv2t2x0000408ihfytl45l1", "model_name": "gemini-1.5-pro-latest", "match_pattern": "(?i)^(gemini-1.5-pro-latest)(@[a-zA-Z0-9]+)?$", "created_at": "2024-04-11T10:27:46.517Z", "updated_at": "2024-04-11T10:27:46.517Z", "prices": {"input": 2.5e-06, "output": 7.5e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cluv2t5k3000508ih5kve9zag", "model_name": "gpt-4-turbo-2024-04-09", "match_pattern": "(?i)^(gpt-4-turbo-2024-04-09)$", "created_at": "2024-04-23T10:37:17.092Z", "updated_at": "2024-04-23T10:37:17.092Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-turbo-2024-04-09", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cluvpl4ls000008l6h2gx3i07", "model_name": "gpt-4-turbo", "match_pattern": "(?i)^(gpt-4-turbo)$", "created_at": "2024-04-11T21:13:44.989Z", "updated_at": "2024-04-11T21:13:44.989Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-1106-preview", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clv2o2x0p000008jsf9afceau", "model_name": " gpt-4-preview", "match_pattern": "(?i)^(gpt-4-preview)$", "created_at": "2024-04-23T10:37:17.092Z", "updated_at": "2024-04-23T10:37:17.092Z", "prices": {"input": 1e-05, "output": 3e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4-turbo-preview", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clx30djsn0000w9mzebiv41we", "model_name": "gemini-1.5-flash", "match_pattern": "(?i)^(gemini-1.5-flash)(@[a-zA-Z0-9]+)?$", "created_at": "2024-06-12T17:09:36.206Z", "updated_at": "2024-06-12T17:09:36.206Z", "prices": {}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "clx30hkrx0000w9mz7lqi0ial", "model_name": "gemini-1.5-pro", "match_pattern": "(?i)^(gemini-1.5-pro)(@[a-zA-Z0-9]+)?$", "created_at": "2024-06-12T17:09:36.206Z", "updated_at": "2024-06-12T17:09:36.206Z", "prices": {}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "clxt0n0m60000pumz1j5b7zsf", "model_name": "claude-3-5-sonnet-20240620", "match_pattern": "(?i)^(claude-3-5-sonnet-20240620|anthropic\\.claude-3-5-sonnet-20240620-v1:0|claude-3-5-sonnet@20240620)$", "created_at": "2024-06-25T11:47:24.475Z", "updated_at": "2024-12-03T12:20:59.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "clyrjp56f0000t0mzapoocd7u", "model_name": "gpt-4o-mini", "match_pattern": "(?i)^(gpt-4o-mini)$", "created_at": "2024-07-18T17:56:09.591Z", "updated_at": "2024-12-03T10:28:30.000Z", "prices": {"input": 1.5e-07, "output": 6e-07, "input_cached_tokens": 7.5e-08, "input_cache_read": 7.5e-08}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clyrjpbe20000t0mzcbwc42rg", "model_name": "gpt-4o-mini-2024-07-18", "match_pattern": "(?i)^(gpt-4o-mini-2024-07-18)$", "created_at": "2024-07-18T17:56:09.591Z", "updated_at": "2024-12-03T10:31:11.000Z", "prices": {"input": 1.5e-07, "input_cached_tokens": 7.5e-08, "input_cache_read": 7.5e-08, "output": 6e-07}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "clzjr85f70000ymmzg7hqffra", "model_name": "gpt-4o-2024-08-06", "match_pattern": "(?i)^(gpt-4o-2024-08-06)$", "created_at": "2024-08-07T11:54:31.298Z", "updated_at": "2024-12-03T10:15:18.000Z", "prices": {"input": 2.5e-06, "input_cached_tokens": 1.25e-06, "input_cache_read": 1.25e-06, "output": 1e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm10ivcdp0000gix7lelmbw80", "model_name": "o1-preview", "match_pattern": "(?i)^(o1-preview)$", "created_at": "2024-09-13T10:01:35.373Z", "updated_at": "2024-12-03T10:34:05.000Z", "prices": {"input": 1.5e-05, "input_cached_tokens": 7.5e-06, "input_cache_read": 7.5e-06, "output": 6e-05, "output_reasoning_tokens": 6e-05, "output_reasoning": 6e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm10ivo130000n8x7qopcjjcg", "model_name": "o1-preview-2024-09-12", "match_pattern": "(?i)^(o1-preview-2024-09-12)$", "created_at": "2024-09-13T10:01:35.373Z", "updated_at": "2024-12-03T10:38:51.000Z", "prices": {"input": 1.5e-05, "input_cached_tokens": 7.5e-06, "input_cache_read": 7.5e-06, "output": 6e-05, "output_reasoning_tokens": 6e-05, "output_reasoning": 6e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm10ivwo40000r1x7gg3syjq0", "model_name": "o1-mini", "match_pattern": "(?i)^(o1-mini)$", "created_at": "2024-09-13T10:01:35.373Z", "updated_at": "2025-02-01T12:41:55.000Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 5.5e-07, "input_cache_read": 5.5e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm10iw6p20000wgx7it1hlb22", "model_name": "o1-mini-2024-09-12", "match_pattern": "(?i)^(o1-mini-2024-09-12)$", "created_at": "2024-09-13T10:01:35.373Z", "updated_at": "2025-02-01T12:41:55.000Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 5.5e-07, "input_cache_read": 5.5e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm2krz1uf000208jjg5653iud", "model_name": "claude-3.5-sonnet-20241022", "match_pattern": "(?i)^(claude-3-5-sonnet-20241022|anthropic\\.claude-3-5-sonnet-20241022-v2:0|claude-3-5-sonnet-V2@20241022)$", "created_at": "2024-10-22T18:48:01.676Z", "updated_at": "2024-12-03T12:21:10.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm2ks2vzn000308jjh4ze1w7q", "model_name": "claude-3.5-sonnet-latest", "match_pattern": "(?i)^(claude-3-5-sonnet-latest)$", "created_at": "2024-10-22T18:48:01.676Z", "updated_at": "2024-12-03T12:22:03.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm34aq60d000207ml0j1h31ar", "model_name": "claude-3-5-haiku-20241022", "match_pattern": "(?i)^(claude-3-5-haiku-20241022|anthropic\\.claude-3-5-haiku-20241022-v1:0|claude-3-5-haiku-V1@20241022)$", "created_at": "2024-11-05T10:30:50.566Z", "updated_at": "2024-12-03T12:24:24.000Z", "prices": {"input": 8e-07, "input_tokens": 8e-07, "output": 4e-06, "output_tokens": 4e-06, "cache_creation_input_tokens": 1e-06, "input_cache_creation": 1e-06, "cache_read_input_tokens": 8e-08, "input_cache_read": 8e-08}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm34aqb9h000307ml6nypd618", "model_name": "claude-3.5-haiku-latest", "match_pattern": "(?i)^(claude-3-5-haiku-latest)$", "created_at": "2024-11-05T10:30:50.566Z", "updated_at": "2024-12-03T12:26:33.000Z", "prices": {"input": 8e-07, "input_tokens": 8e-07, "output": 4e-06, "output_tokens": 4e-06, "cache_creation_input_tokens": 1e-06, "input_cache_creation": 1e-06, "cache_read_input_tokens": 8e-08, "input_cache_read": 8e-08}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm3x0p8ev000008kyd96800c8", "model_name": "chatgpt-4o-latest", "match_pattern": "(?i)^(chatgpt-4o-latest)$", "created_at": "2024-11-25T12:47:17.504Z", "updated_at": "2024-11-25T12:47:17.504Z", "prices": {"input": 5e-06, "output": 1.5e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm48akqgo000008ldbia24qg0", "model_name": "gpt-4o-2024-11-20", "match_pattern": "(?i)^(gpt-4o-2024-11-20)$", "created_at": "2024-12-03T10:06:12.000Z", "updated_at": "2024-12-03T10:06:12.000Z", "prices": {"input": 2.5e-06, "input_cached_tokens": 1.25e-06, "input_cache_read": 1.25e-06, "output": 1e-05}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4o", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm48b2ksh000008l0hn3u0hl3", "model_name": "gpt-4o-audio-preview", "match_pattern": "(?i)^(gpt-4o-audio-preview)$", "created_at": "2024-12-03T10:19:56.000Z", "updated_at": "2024-12-03T10:19:56.000Z", "prices": {"input_text_tokens": 2.5e-06, "output_text_tokens": 1e-05, "input_audio_tokens": 0.0001, "input_audio": 0.0001, "output_audio_tokens": 0.0002, "output_audio": 0.0002}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48bbm0k000008l69nsdakwf", "model_name": "gpt-4o-audio-preview-2024-10-01", "match_pattern": "(?i)^(gpt-4o-audio-preview-2024-10-01)$", "created_at": "2024-12-03T10:19:56.000Z", "updated_at": "2024-12-03T10:19:56.000Z", "prices": {"input_text_tokens": 2.5e-06, "output_text_tokens": 1e-05, "input_audio_tokens": 0.0001, "input_audio": 0.0001, "output_audio_tokens": 0.0002, "output_audio": 0.0002}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48c2qh4000008mhgy4mg2qc", "model_name": "gpt-4o-realtime-preview", "match_pattern": "(?i)^(gpt-4o-realtime-preview)$", "created_at": "2024-12-03T10:19:56.000Z", "updated_at": "2024-12-03T10:19:56.000Z", "prices": {"input_text_tokens": 5e-06, "input_cached_text_tokens": 2.5e-06, "output_text_tokens": 2e-05, "input_audio_tokens": 0.0001, "input_audio": 0.0001, "input_cached_audio_tokens": 2e-05, "output_audio_tokens": 0.0002, "output_audio": 0.0002}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48cjxtc000008jrcsso3avv", "model_name": "gpt-4o-realtime-preview-2024-10-01", "match_pattern": "(?i)^(gpt-4o-realtime-preview-2024-10-01)$", "created_at": "2024-12-03T10:19:56.000Z", "updated_at": "2024-12-03T10:19:56.000Z", "prices": {"input_text_tokens": 5e-06, "input_cached_text_tokens": 2.5e-06, "output_text_tokens": 2e-05, "input_audio_tokens": 0.0001, "input_audio": 0.0001, "input_cached_audio_tokens": 2e-05, "output_audio_tokens": 0.0002, "output_audio": 0.0002}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48cjxtc000108jrcsso3avv", "model_name": "o1", "match_pattern": "(?i)^(o1)$", "created_at": "2025-01-17T00:01:35.373Z", "updated_at": "2025-01-17T00:01:35.373Z", "prices": {"input": 1.5e-05, "input_cached_tokens": 7.5e-06, "input_cache_read": 7.5e-06, "output": 6e-05, "output_reasoning_tokens": 6e-05, "output_reasoning": 6e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48cjxtc000208jrcsso3avv", "model_name": "o1-2024-12-17", "match_pattern": "(?i)^(o1-2024-12-17)$", "created_at": "2025-01-17T00:01:35.373Z", "updated_at": "2025-01-17T00:01:35.373Z", "prices": {"input": 1.5e-05, "input_cached_tokens": 7.5e-06, "input_cache_read": 7.5e-06, "output": 6e-05, "output_reasoning_tokens": 6e-05, "output_reasoning": 6e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm48cjxtc000308jrcsso3avv", "model_name": "gemini-2.0-flash-exp", "match_pattern": "(?i)^(gemini-2.0-flash-exp)(@[a-zA-Z0-9]+)?$", "created_at": "2025-01-17T11:11:35.241Z", "updated_at": "2025-01-17T11:11:35.241Z", "prices": {}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm6l8j7vs0000tymz9vk7ew8t", "model_name": "o3-mini", "match_pattern": "(?i)^(o3-mini)$", "created_at": "2025-01-31T20:41:35.373Z", "updated_at": "2025-01-31T20:41:35.373Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 5.5e-07, "input_cache_read": 5.5e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm6l8jan90000tymz52sh0ql8", "model_name": "o3-mini-2025-01-31", "match_pattern": "(?i)^(o3-mini-2025-01-31)$", "created_at": "2025-01-31T20:41:35.373Z", "updated_at": "2025-01-31T20:41:35.373Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 5.5e-07, "input_cache_read": 5.5e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm6l8jbcd0000tymz52sh0ql9", "model_name": "gemini-2.0-pro-exp-02-05", "match_pattern": "(?i)^(gemini-2.0-pro-exp-02-05)(@[a-zA-Z0-9]+)?$", "created_at": "2025-02-06T11:11:35.241Z", "updated_at": "2025-02-06T11:11:35.241Z", "prices": {}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm6l8jdef0000tymz52sh0ql0", "model_name": "gemini-2.0-flash-001", "match_pattern": "(?i)^(gemini-2.0-flash-001)(@[a-zA-Z0-9]+)?$", "created_at": "2025-02-06T11:11:35.241Z", "updated_at": "2025-02-10T10:11:35.241Z", "prices": {"input": 1e-07, "output": 4e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm6l8jfgh0000tymz52sh0ql1", "model_name": "gemini-2.0-flash-lite-preview-02-05", "match_pattern": "(?i)^(gemini-2.0-flash-lite-preview-02-05)(@[a-zA-Z0-9]+)?$", "created_at": "2025-02-06T11:11:35.241Z", "updated_at": "2025-02-10T10:11:35.241Z", "prices": {"input": 7.5e-08, "output": 3e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7ka7561000108js3t9tb3at", "model_name": "claude-3.7-sonnet-20250219", "match_pattern": "(?i)^(claude-3.7-sonnet-20250219|anthropic\\.claude-3.7-sonnet-20250219-v1:0|claude-3-7-sonnet-V1@20250219)$", "created_at": "2025-02-25T09:35:39.000Z", "updated_at": "2025-02-27T12:07:29.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm7ka7zob000208jsfs9h5ajj", "model_name": "claude-3.7-sonnet-latest", "match_pattern": "(?i)^(claude-3-7-sonnet-latest)$", "created_at": "2025-02-25T09:35:39.000Z", "updated_at": "2025-02-25T09:35:39.000Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cm7nusjvk0000tvmz71o85jwg", "model_name": "gpt-4.5-preview", "match_pattern": "(?i)^(gpt-4.5-preview)$", "created_at": "2025-02-27T21:26:54.132Z", "updated_at": "2025-02-27T21:26:54.132Z", "prices": {"input": 7.5e-05, "input_cached_tokens": 3.75e-05, "input_cached_text_tokens": 3.75e-05, "input_cache_read": 3.75e-05, "output": 0.00015}}, {"id": "cm7nusn640000tvmzf10z2x65", "model_name": "gpt-4.5-preview-2025-02-27", "match_pattern": "(?i)^(gpt-4.5-preview-2025-02-27)$", "created_at": "2025-02-27T21:26:54.132Z", "updated_at": "2025-02-27T21:26:54.132Z", "prices": {"input": 7.5e-05, "input_cached_tokens": 3.75e-05, "input_cached_text_tokens": 3.75e-05, "input_cache_read": 3.75e-05, "output": 0.00015}}, {"id": "cm7nusn643377tvmzh27m33kl", "model_name": "gpt-4.1", "match_pattern": "(?i)^(gpt-4.1)$", "created_at": "2025-04-15T10:26:54.132Z", "updated_at": "2025-04-15T10:26:54.132Z", "prices": {"input": 2e-06, "input_cached_tokens": 5e-07, "input_cached_text_tokens": 5e-07, "input_cache_read": 5e-07, "output": 8e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm7qahw732891bpmzy45r3x70", "model_name": "gpt-4.1-2025-04-14", "match_pattern": "(?i)^(gpt-4.1-2025-04-14)$", "created_at": "2025-04-15T10:26:54.132Z", "updated_at": "2025-04-15T10:26:54.132Z", "prices": {"input": 2e-06, "input_cached_tokens": 5e-07, "input_cached_text_tokens": 5e-07, "input_cache_read": 5e-07, "output": 8e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm7sglt825463kxnza72p6v81", "model_name": "gpt-4.1-mini-2025-04-14", "match_pattern": "(?i)^(gpt-4.1-mini-2025-04-14)$", "created_at": "2025-04-15T10:26:54.132Z", "updated_at": "2025-04-15T10:26:54.132Z", "prices": {"input": 4e-07, "input_cached_tokens": 1e-07, "input_cached_text_tokens": 1e-07, "input_cache_read": 1e-07, "output": 1.6e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm7vxpz967124dhjtb95w8f92", "model_name": "gpt-4.1-nano-2025-04-14", "match_pattern": "(?i)^(gpt-4.1-nano-2025-04-14)$", "created_at": "2025-04-15T10:26:54.132Z", "updated_at": "2025-04-15T10:26:54.132Z", "prices": {"input": 1e-07, "input_cached_tokens": 2.5e-08, "input_cached_text_tokens": 2.5e-08, "input_cache_read": 2.5e-08, "output": 4e-07}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm7wmny967124dhjtb95w8f81", "model_name": "o3", "match_pattern": "(?i)^(o3)$", "created_at": "2025-04-16T23:26:54.132Z", "updated_at": "2025-06-10T23:26:54.132Z", "prices": {"input": 2e-06, "input_cached_tokens": 5e-07, "input_cache_read": 5e-07, "output": 8e-06, "output_reasoning_tokens": 8e-06, "output_reasoning": 8e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7wopq3327124dhjtb95w8f81", "model_name": "o3-2025-04-16", "match_pattern": "(?i)^(o3-2025-04-16)$", "created_at": "2025-04-16T23:26:54.132Z", "updated_at": "2025-06-10T23:26:54.132Z", "prices": {"input": 2e-06, "input_cached_tokens": 5e-07, "input_cache_read": 5e-07, "output": 8e-06, "output_reasoning_tokens": 8e-06, "output_reasoning": 8e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7wqrs1327124dhjtb95w8f81", "model_name": "o4-mini", "match_pattern": "(?i)^(o4-mini)$", "created_at": "2025-04-16T23:26:54.132Z", "updated_at": "2025-04-16T23:26:54.132Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 2.75e-07, "input_cache_read": 2.75e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7zqrs1327124dhjtb95w8f82", "model_name": "o4-mini-2025-04-16", "match_pattern": "(?i)^(o4-mini-2025-04-16)$", "created_at": "2025-04-16T23:26:54.132Z", "updated_at": "2025-04-16T23:26:54.132Z", "prices": {"input": 1.1e-06, "input_cached_tokens": 2.75e-07, "input_cache_read": 2.75e-07, "output": 4.4e-06, "output_reasoning_tokens": 4.4e-06, "output_reasoning": 4.4e-06}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7zrrs1327124dhjtb95w8f13", "model_name": "gemini-2.0-pro-exp", "match_pattern": "(?i)^(gemini-2.0-pro-exp)(@[a-zA-Z0-9]+)?$", "created_at": "2025-04-22T10:11:35.241Z", "updated_at": "2025-04-22T10:11:35.241Z", "prices": {}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7zsrs1327124dhjtb95w8f74", "model_name": "gemini-2.0-flash", "match_pattern": "(?i)^(gemini-2.0-flash)(@[a-zA-Z0-9]+)?$", "created_at": "2025-04-22T10:11:35.241Z", "updated_at": "2025-04-22T10:11:35.241Z", "prices": {"input": 1e-07, "output": 4e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7ztrs1327124dhjtb95w8f19", "model_name": "gemini-2.0-flash-lite-preview", "match_pattern": "(?i)^(gemini-2.0-flash-lite-preview)(@[a-zA-Z0-9]+)?$", "created_at": "2025-04-22T10:11:35.241Z", "updated_at": "2025-04-22T10:11:35.241Z", "prices": {"input": 7.5e-08, "output": 3e-07}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cm7zxrs1327124dhjtb95w8f45", "model_name": "gpt-4.1-nano", "match_pattern": "(?i)^(gpt-4.1-nano)$", "created_at": "2025-04-22T10:11:35.241Z", "updated_at": "2025-04-22T10:11:35.241Z", "prices": {"input": 1e-07, "input_cached_tokens": 2.5e-08, "input_cached_text_tokens": 2.5e-08, "input_cache_read": 2.5e-08, "output": 4e-07}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cm7zzrs1327124dhjtb95w8p96", "model_name": "gpt-4.1-mini", "match_pattern": "(?i)^(gpt-4.1-mini)$", "created_at": "2025-04-22T10:11:35.241Z", "updated_at": "2025-04-22T10:11:35.241Z", "prices": {"input": 4e-07, "input_cached_tokens": 1e-07, "input_cached_text_tokens": 1e-07, "input_cache_read": 1e-07, "output": 1.6e-06}, "tokenizer_config": {"tokensPerName": 1, "tokenizerModel": "gpt-4", "tokensPerMessage": 3}, "tokenizer_id": "openai"}, {"id": "cmazmkzlm00000djp1e1qe4k4", "model_name": "claude-sonnet-4-20250514", "match_pattern": "(?i)^(claude-sonnet-4-20250514|anthropic\\.claude-sonnet-4-20250514-v1:0|claude-sonnet-4-V1@20250514|claude-sonnet-4@20250514)$", "created_at": "2025-05-22T17:09:02.131Z", "updated_at": "2025-05-26T23:09:02.131Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cmazmlbnv00010djpazed91va", "model_name": "claude-sonnet-4-latest", "match_pattern": "(?i)^(claude-sonnet-4-latest)$", "created_at": "2025-05-22T17:09:02.131Z", "updated_at": "2025-05-22T17:09:02.131Z", "prices": {"input": 3e-06, "input_tokens": 3e-06, "output": 1.5e-05, "output_tokens": 1.5e-05, "cache_creation_input_tokens": 3.75e-06, "input_cache_creation": 3.75e-06, "cache_read_input_tokens": 3e-07, "input_cache_read": 3e-07}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cmazmlm2p00020djpa9s64jw5", "model_name": "claude-opus-4-20250514", "match_pattern": "(?i)^(claude-opus-4-20250514|anthropic\\.claude-opus-4-20250514-v1:0|claude-opus-4@20250514)$", "created_at": "2025-05-22T17:09:02.131Z", "updated_at": "2025-05-22T17:09:02.131Z", "prices": {"input": 1.5e-05, "input_tokens": 1.5e-05, "output": 7.5e-05, "output_tokens": 7.5e-05, "cache_creation_input_tokens": 1.875e-05, "input_cache_creation": 1.875e-05, "cache_read_input_tokens": 1.5e-06, "input_cache_read": 1.5e-06}, "tokenizer_config": null, "tokenizer_id": "claude"}, {"id": "cmz9x72kq55721pqrs83y4n2bx", "model_name": "o3-pro", "match_pattern": "(?i)^(o3-pro)$", "created_at": "2025-06-10T22:26:54.132Z", "updated_at": "2025-06-10T22:26:54.132Z", "prices": {"input": 2e-05, "output": 8e-05, "output_reasoning_tokens": 8e-05, "output_reasoning": 8e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cmz9x72kq55721pqrs83y4n2by", "model_name": "o3-pro-2025-06-10", "match_pattern": "(?i)^(o3-pro-2025-06-10)$", "created_at": "2025-06-10T22:26:54.132Z", "updated_at": "2025-06-10T22:26:54.132Z", "prices": {"input": 2e-05, "output": 8e-05, "output_reasoning_tokens": 8e-05, "output_reasoning": 8e-05}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cmbrold5b000107lbftb9fdoo", "model_name": "o1-pro", "match_pattern": "(?i)^(o1-pro)$", "created_at": "2025-06-10T22:26:54.132Z", "updated_at": "2025-06-10T22:26:54.132Z", "prices": {"input": 0.00015, "output": 0.0006, "output_reasoning_tokens": 0.0006, "output_reasoning": 0.0006}, "tokenizer_config": null, "tokenizer_id": null}, {"id": "cmbrolpax000207lb3xkedysz", "model_name": "o1-pro-2025-03-19", "match_pattern": "(?i)^(o1-pro-2025-03-19)$", "created_at": "2025-06-10T22:26:54.132Z", "updated_at": "2025-06-10T22:26:54.132Z", "prices": {"input": 0.00015, "output": 0.0006, "output_reasoning_tokens": 0.0006, "output_reasoning": 0.0006}, "tokenizer_config": null, "tokenizer_id": null}]
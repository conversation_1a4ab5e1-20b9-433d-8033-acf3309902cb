#!/bin/bash

echo "修复 PostgreSQL 初始化问题..."

# 停止所有服务
echo "停止服务..."
docker-compose down

# 清理 PostgreSQL 相关的容器和卷
echo "清理 PostgreSQL 数据..."
docker volume rm $(docker volume ls -q | grep postgres) 2>/dev/null || true

# 清理可能存在的容器
docker rm -f $(docker ps -aq --filter "ancestor=postgres") 2>/dev/null || true

# 拉取 PostgreSQL 15 镜像（如果还没有）
echo "拉取 PostgreSQL 15 镜像..."
docker pull --platform linux/amd64 postgres:15

# 重新创建卷
echo "重新创建数据卷..."
docker volume create langfuse_langfuse_postgres_data

# 启动服务
echo "启动服务..."
docker-compose up -d postgres

# 等待 PostgreSQL 启动
echo "等待 PostgreSQL 启动..."
sleep 30

# 检查 PostgreSQL 状态
echo "检查 PostgreSQL 状态..."
docker-compose logs postgres

echo "修复完成！"

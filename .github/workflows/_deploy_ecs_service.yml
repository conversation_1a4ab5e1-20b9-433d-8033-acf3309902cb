name: WorkflowCall - Deploy to ECS Service
on:
  workflow_call:
    inputs:
      environment:
        type: string
        description: Deployment environment
        required: true
      service:
        type: string
        description: Name of the service to be deployed, e.g. web-ingestion, web, or worker.
        required: true
jobs:
  ecs-deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:
      - name: Set Swap Space
        uses: pierotofy/set-swap-space@master
        with:
          swap-size-gb: 10
      - name: Get app name
        uses: winterjung/split@v2
        id: split
        with:
          msg: ${{ inputs.service }}
          separator: "-"
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Authenticate with AWS
        # GitHub/AWS recommend to use OIDC here: https://github.com/aws-actions/configure-aws-credentials?tab=readme-ov-file#oidc
        # Probably more painful to configure, but would remove all long-lived credentials.
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}
      - name: Login to AWS ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build, tag, and push Docker image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ steps.split.outputs._0 }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build \
            -t $REGISTRY/$REPOSITORY:$IMAGE_TAG \
            -f ./${{ steps.split.outputs._0 }}/Dockerfile \
            --build-arg NEXT_PUBLIC_LANGFUSE_CLOUD_REGION=${{ vars.NEXT_PUBLIC_LANGFUSE_CLOUD_REGION }} \
            --build-arg NEXT_LANGFUSE_TRACING_SAMPLE_RATE=${{ vars.NEXT_LANGFUSE_TRACING_SAMPLE_RATE }} \
            --build-arg NEXT_PUBLIC_SENTRY_ENVIRONMENT=${{ vars.NEXT_PUBLIC_SENTRY_ENVIRONMENT }} \
            --build-arg NEXT_PUBLIC_DEMO_ORG_ID=${{ vars.NEXT_PUBLIC_DEMO_ORG_ID }} \
            --build-arg NEXT_PUBLIC_DEMO_PROJECT_ID=${{ vars.NEXT_PUBLIC_DEMO_PROJECT_ID }} \
            --build-arg NEXT_PUBLIC_SENTRY_DSN=${{ vars.NEXT_PUBLIC_SENTRY_DSN }} \
            --build-arg NEXT_PUBLIC_BUILD_ID=${{ github.sha }} \
            --build-arg NEXT_PUBLIC_POSTHOG_KEY=${{ vars.NEXT_PUBLIC_POSTHOG_KEY }} \
            --build-arg NEXT_PUBLIC_POSTHOG_HOST=${{ vars.NEXT_PUBLIC_POSTHOG_HOST }} \
            --build-arg NEXT_PUBLIC_PLAIN_APP_ID=${{ vars.NEXT_PUBLIC_PLAIN_APP_ID }} \
            --build-arg SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }} \
            --build-arg SENTRY_ORG=${{ vars.SENTRY_ORG }} \
            --build-arg SENTRY_PROJECT=${{ vars.SENTRY_PROJECT }} \
            --build-arg NEXT_PUBLIC_LANGFUSE_TRACING_SAMPLE_RATE=${{ vars.NEXT_PUBLIC_LANGFUSE_TRACING_SAMPLE_RATE }} \
            .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
      - name: Render AWS ECS Task Definition
        id: render-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          container-name: ${{ inputs.service }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ steps.split.outputs._0 }}:${{ github.sha }}
          task-definition-family: ${{ inputs.environment }}-${{ inputs.service }}
      - name: Update AWS ECS Service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.render-task-definition.outputs.task-definition }}
          service: ${{ inputs.environment }}-${{ inputs.service }}
          cluster: ${{ inputs.environment }}-cluster
          wait-for-service-stability: true
